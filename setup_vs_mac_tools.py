"""
Setup script to create a macOS .app bundle for VS Mac Tools v2.1.0.
This script uses py2app to create a standalone macOS application.

Compatible with macOS Sierra (10.12) through macOS Sequoia (15.0)
"""

from setuptools import setup
import os
import sys
import shutil
from pathlib import Path
import py2app.build_app

# Monkey patch py2app to disable code signing
py2app.build_app.codesign_adhoc = lambda x: True

# Ensure we're using the correct Python interpreter
if sys.prefix == sys.base_prefix:
    print("WARNING: You are not using a virtual environment.")
    print("It's recommended to use a virtual environment for building the application.")

# Application name and version
APP_NAME = 'VS Mac Tools'
VERSION = '2.1.0'

# Ensure the img directory exists and is copied to the bundle
img_dir = Path('img')
if not img_dir.exists():
    os.makedirs('img', exist_ok=True)
    print("Created img directory. Please add your icon files before building.")

# Define the application icon
ICON = 'img/vsmactool.icns'
if not os.path.exists(ICON):
    # Fallback to other available icons
    fallback_icons = ['img/vs_mac_tool_v2.icns', 'img/checkADE.icns', 'checkADE.icns', 'panda.icns']
    for fallback in fallback_icons:
        if os.path.exists(fallback):
            ICON = fallback
            break
    else:
        ICON = None
        print("Warning: No icon file found. Building without icon.")

# Define py2app options
OPTIONS = {
    'argv_emulation': False,  # Disable argv_emulation which can cause issues
    'iconfile': ICON,
    'plist': {
        'CFBundleName': APP_NAME,
        'CFBundleDisplayName': APP_NAME,
        'CFBundleGetInfoString': f"{APP_NAME} {VERSION}",
        'CFBundleIdentifier': "com.vonzki.vsmactools",
        'CFBundleVersion': VERSION,
        'CFBundleShortVersionString': VERSION,
        'NSHumanReadableCopyright': '© 2024 by vonzki',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.12',  # macOS Sierra for wider compatibility
        'NSRequiresAquaSystemAppearance': False,  # Support dark mode
        'NSPrincipalClass': 'NSApplication',  # Required for proper macOS integration
        'NSAppleScriptEnabled': False,
        'CFBundleDocumentTypes': [],
        'LSApplicationCategoryType': 'public.app-category.utilities',
        'LSUIElement': False,  # Show in dock
        'NSAppTransportSecurity': {
            'NSAllowsArbitraryLoads': True  # Allow network connections
        }
    },
    'packages': [
        'customtkinter',
        'PIL',
        'tkinter',
        'darkdetect',  # Required by customtkinter
        'keyring',
        'queue',
        'threading',
        'socket',
        'subprocess',
        'xml',
        'csv',
        'pathlib',
        'platform',
        'getpass'
    ],
    'includes': [
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'xml.dom.minidom',
        'xml.etree.ElementTree',
        'packaging.version',  # Required by customtkinter
        'socket',
        'subprocess',
        'threading',
        'os',
        'time',
        'csv',
        'datetime',
        'queue',
        'pathlib',
        'platform',
        'keyring',
        'getpass',
        'password_dialog'
    ],
    'resources': ['img'],
    'frameworks': [],
    'excludes': [
        'matplotlib', 'numpy', 'scipy', 'pandas', 
        'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
        'jupyter', 'IPython', 'notebook'
    ],
    'strip': False,  # Don't strip debug symbols for better error reporting
    'optimize': 0,   # Don't optimize bytecode for better error reporting
    'semi_standalone': False,  # Create a fully standalone app
    'site_packages': True,
    'use_faulthandler': True,  # Enable fault handler for better crash reports
    'dylib_excludes': ['libSystem.dylib'],
    'no_chdir': True,  # Don't change working directory
}

setup(
    name=APP_NAME,
    app=['vs_mac_tools_v2.1.0.py'],  # Use the main script as entry point
    version=VERSION,
    options={'py2app': OPTIONS},
    setup_requires=['py2app'],
    install_requires=[
        'customtkinter>=5.2.0',
        'pillow>=9.0.0',
        'keyring>=23.0.0',
    ],
    author='vonzki',
    author_email='<EMAIL>',
    description='VS Mac Tools - Apple Device Enrollment and System Management Tool',
    long_description='A comprehensive tool for checking Apple Device Enrollment status, managing system settings, and performing various macOS administrative tasks.',
    url='https://github.com/vonzki/vs-mac-tools',
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: System Administrators',
        'License :: OSI Approved :: MIT License',
        'Operating System :: MacOS',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Programming Language :: Python :: 3.12',
        'Topic :: System :: Systems Administration',
        'Topic :: Utilities',
    ],
)

print(f"\nBuild completed for {APP_NAME} v{VERSION}!")
print("\nTo create the application bundle, run:")
print("python setup_vs_mac_tools.py py2app")
print("\nAfter building, the .app will be in the 'dist' folder.")
print("You can then copy it to your Applications folder.")
