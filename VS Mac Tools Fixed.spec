# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['vs_mac_tools_v2.1.0_fixed.py'],
    pathex=[],
    binaries=[],
    datas=[('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets', 'customtkinter/assets')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VS Mac Tools Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VS Mac Tools Fixed',
)
app = BUNDLE(
    coll,
    name='VS Mac Tools Fixed.app',
    icon=None,
    bundle_identifier=None,
)
