#!/bin/bash

# Script to fix code signing issues with an existing Apple ADE Checker application
# This script addresses the "Code Signature Invalid" error

# Check if the application path is provided
if [ $# -eq 0 ]; then
    # No path provided, use the default path
    APP_PATH="dist/VS Mac Tools.app"
else
    # Use the provided path
    APP_PATH="$1"
fi

# Check if the application exists
if [ ! -d "$APP_PATH" ]; then
    echo "Error: Application not found at $APP_PATH"
    echo "Please provide the correct path to the application."
    echo "Usage: $0 [path/to/VS Mac Tools.app]"
    exit 1
fi

echo "Fixing code signing for: $APP_PATH"

# Remove existing code signatures
echo "Removing existing code signatures..."
find "$APP_PATH" -name "_CodeSignature" -type d -exec rm -rf {} \; 2>/dev/null || true

# Fix file permissions
echo "Fixing file permissions..."
find "$APP_PATH" -type f -exec chmod 644 {} \;
find "$APP_PATH" -type d -exec chmod 755 {} \;
chmod 755 "$APP_PATH/Contents/MacOS/"*

# Create a simple entitlements file
echo "Creating entitlements file..."
cat > entitlements.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>
</dict>
</plist>
EOF

# Sign all the dynamic libraries and executables
echo "Signing Python libraries and frameworks..."
find "$APP_PATH" -name "*.so" -o -name "*.dylib" | while read file; do
    codesign --force --sign - --timestamp=none --preserve-metadata=identifier,entitlements,flags --entitlements entitlements.plist "$file"
done

# Sign the Python.framework if it exists
if [ -d "$APP_PATH/Contents/Frameworks/Python.framework" ]; then
    echo "Signing Python.framework..."
    codesign --force --sign - --timestamp=none --preserve-metadata=identifier,entitlements,flags --entitlements entitlements.plist "$APP_PATH/Contents/Frameworks/Python.framework"
fi

# Sign all executables in MacOS directory
echo "Signing executables..."
find "$APP_PATH/Contents/MacOS" -type f | while read file; do
    codesign --force --sign - --timestamp=none --preserve-metadata=identifier,entitlements,flags --entitlements entitlements.plist "$file"
done

# Finally, sign the application bundle
echo "Signing application bundle..."
codesign --force --sign - --timestamp=none --preserve-metadata=identifier,entitlements,flags --entitlements entitlements.plist "$APP_PATH"

# Verify the signature
echo "Verifying signature..."
codesign --verify --verbose "$APP_PATH"

# Clean up
rm entitlements.plist

echo "Code signing completed!"
echo "The application should now run without code signing errors."
echo ""
echo "To run the application, use:"
echo "open \"$APP_PATH\""
