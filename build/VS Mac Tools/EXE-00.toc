('/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
 'Mac Tools/VS Mac Tools',
 Fals<PERSON>,
 <PERSON>als<PERSON>,
 True,
 ['/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vsmactool.icns'],
 None,
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 None,
 True,
 <PERSON><PERSON><PERSON>,
 'arm64',
 None,
 None,
 '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
 'Mac Tools/VS Mac Tools.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('vs_mac_tools_v2.1.0',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/vs_mac_tools_v2.1.0.py',
   'PYSOURCE')],
 [],
 False,
 False,
 1749518374,
 [('runw',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/bootloader/Darwin-64bit/runw',
   'EXECUTABLE')],
 '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Python3')
