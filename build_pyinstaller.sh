#!/bin/bash

# Script to build VS Mac Tools v2.1.0 using PyInstaller
# This creates a standalone macOS .app bundle

set -e  # Exit on any error

echo "🚀 Building VS Mac Tools v2.1.0 with PyInstaller"
echo "================================================"

# Check if we're in the right directory
if [ ! -f "vs_mac_tools_v2.1.0.py" ]; then
    echo "❌ Error: vs_mac_tools_v2.1.0.py not found in current directory"
    echo "Please run this script from the project root directory"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "⚠️  Virtual environment not found. Creating one..."
    python3 -m venv venv
    echo "✅ Virtual environment created"
fi

# Activate the virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip and install required packages
echo "📦 Installing/upgrading required packages..."
pip install --upgrade pip
pip install --upgrade pyinstaller
pip install --upgrade customtkinter>=5.2.0
pip install --upgrade pillow>=9.0.0
pip install --upgrade keyring>=23.0.0

# Install any additional requirements if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📋 Installing requirements from requirements.txt..."
    pip install -r requirements.txt
fi

# Clean up previous builds
echo "🧹 Cleaning up previous builds..."
rm -rf build dist
rm -rf *.spec
rm -rf __pycache__

# Check for icon file
ICON_FILE=""
if [ -f "img/vsmactool.icns" ]; then
    ICON_FILE="img/vsmactool.icns"
    echo "📎 Using icon: $ICON_FILE"
elif [ -f "img/vs_mac_tool_v2.icns" ]; then
    ICON_FILE="img/vs_mac_tool_v2.icns"
    echo "📎 Using fallback icon: $ICON_FILE"
elif [ -f "img/checkADE.icns" ]; then
    ICON_FILE="img/checkADE.icns"
    echo "📎 Using fallback icon: $ICON_FILE"
else
    echo "⚠️  No icon file found. Building without icon."
fi

# Ensure password_dialog.py is available
if [ ! -f "password_dialog.py" ]; then
    echo "❌ Error: password_dialog.py not found"
    echo "This file is required for the application to work properly"
    exit 1
fi

# Get CustomTkinter path for data files
echo "🔍 Locating CustomTkinter assets..."
CTK_PATH=$(python -c "import customtkinter; import os; print(os.path.dirname(customtkinter.__file__))")
echo "   CustomTkinter path: $CTK_PATH"

# Build the application with PyInstaller
echo "🔨 Building with PyInstaller (with CustomTkinter assets)..."
echo "This may take several minutes..."

# Construct PyInstaller command
PYINSTALLER_CMD="pyinstaller \
    --name='VS Mac Tools' \
    --onedir \
    --windowed \
    --clean \
    --noconfirm \
    --distpath=dist \
    --workpath=build \
    --specpath=. \
    --add-data=img:img \
    --add-data=password_dialog.py:. \
    --add-data=$CTK_PATH/assets:customtkinter/assets \
    --hidden-import=customtkinter \
    --hidden-import=customtkinter.windows \
    --hidden-import=customtkinter.windows.widgets \
    --hidden-import=customtkinter.windows.ctk_tk \
    --hidden-import=customtkinter.windows.ctk_toplevel \
    --hidden-import=darkdetect \
    --hidden-import=PIL \
    --hidden-import=PIL._tkinter_finder \
    --hidden-import=keyring \
    --hidden-import=keyring.backends \
    --hidden-import=keyring.backends.macOS \
    --hidden-import=password_dialog \
    --hidden-import=tkinter \
    --hidden-import=tkinter.filedialog \
    --hidden-import=tkinter.messagebox \
    --hidden-import=xml.dom.minidom \
    --hidden-import=xml.etree.ElementTree \
    --hidden-import=queue \
    --hidden-import=threading \
    --hidden-import=socket \
    --hidden-import=subprocess \
    --hidden-import=platform \
    --hidden-import=getpass \
    --hidden-import=pathlib \
    --hidden-import=csv \
    --hidden-import=datetime \
    --hidden-import=time \
    --hidden-import=os \
    --hidden-import=sys"

# Add icon if available
if [ ! -z "$ICON_FILE" ]; then
    PYINSTALLER_CMD="$PYINSTALLER_CMD --icon=$ICON_FILE"
fi

# Add the main script
PYINSTALLER_CMD="$PYINSTALLER_CMD vs_mac_tools_v2.1.0.py"

# Execute the command
eval $PYINSTALLER_CMD

# Check if the build was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    
    # Check if the .app was created
    if [ -d "dist/VS Mac Tools.app" ]; then
        echo "📱 Application created: dist/VS Mac Tools.app"
        
        # Get the size of the .app bundle
        app_size=$(du -sh "dist/VS Mac Tools.app" | cut -f1)
        echo "📏 Application size: $app_size"
        
        # Test if the app can be launched
        echo "🧪 Testing application launch..."
        if open "dist/VS Mac Tools.app" 2>/dev/null; then
            echo "✅ Application launches successfully"
        else
            echo "⚠️  Warning: Application may have launch issues"
        fi
        
        echo ""
        echo "🎉 VS Mac Tools v2.1.0 has been successfully built with PyInstaller!"
        echo ""
        echo "📍 Location: $(pwd)/dist/VS Mac Tools.app"
        echo ""
        echo "📋 Next steps:"
        echo "   1. Test the application by double-clicking it"
        echo "   2. Copy it to your Applications folder:"
        echo "      cp -R 'dist/VS Mac Tools.app' /Applications/"
        echo "   3. Or create a disk image for distribution:"
        echo "      hdiutil create -volname 'VS Mac Tools' -srcfolder 'dist/VS Mac Tools.app' -ov -format UDZO 'VS Mac Tools v2.1.0.dmg'"
        echo ""
        echo "💡 PyInstaller advantages:"
        echo "   • More reliable than py2app"
        echo "   • Better dependency detection"
        echo "   • Fewer code signing issues"
        echo "   • Cross-platform compatibility"
        echo ""
        
    else
        echo "❌ Error: Application bundle not found in dist/ directory"
        exit 1
    fi
else
    echo "❌ Build failed. Check the error messages above."
    echo ""
    echo "🔍 Common issues and solutions:"
    echo "   1. Missing dependencies: Make sure all required packages are installed"
    echo "   2. Python version: Ensure you're using Python 3.8 or later"
    echo "   3. macOS version: This script requires macOS 10.12 or later"
    echo "   4. Virtual environment: Make sure venv is properly activated"
    echo ""
    exit 1
fi

echo "🏁 PyInstaller build process completed!"
