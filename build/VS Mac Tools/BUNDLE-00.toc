([('VS Mac Tools',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools/VS Mac Tools',
   'EXECUTABLE'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('Python3.framework/Versions/3.9/Python3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Python3',
   'BINARY'),
  ('lib-dynload/_statistics.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_statistics.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_contextvars.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_decimal.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_pickle.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_hashlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha3.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_blake2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha256.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_md5.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha1.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_sha512.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_random.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bisect.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/math.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/unicodedata.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/resource.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_lzma.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_bz2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/grp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/binascii.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_opcode.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixshmem.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/pyexpat.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_scproxy.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ssl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/select.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/mmap.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_ctypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/array.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_asyncio.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/readline.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/termios.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/syslog.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_json.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-39-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/_imagingtk.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_heapq.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-39-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/_webp.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-39-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/_imagingcms.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-39-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/_imagingmath.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-39-darwin.so',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/_imaging.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_queue.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_tkinter.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_elementtree.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_csv.cpython-39-darwin.so',
   'EXTENSION'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.3.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libopenjp2.2.5.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/libXau.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf',
   'DATA'),
  ('customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter/assets/themes/blue.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets/themes/blue.json',
   'DATA'),
  ('customtkinter/assets/themes/dark-blue.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets/themes/dark-blue.json',
   'DATA'),
  ('customtkinter/assets/themes/green.json',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets/themes/green.json',
   'DATA'),
  ('img/123.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/123.webp',
   'DATA'),
  ('img/bluetooth.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/bluetooth.icns',
   'DATA'),
  ('img/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/device.icns',
   'DATA'),
  ('img/erase.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/erase.png',
   'DATA'),
  ('img/exit.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/exit.png',
   'DATA'),
  ('img/findmy.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/findmy.icns',
   'DATA'),
  ('img/shutdown.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/shutdown.png',
   'DATA'),
  ('img/sysinfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/sysinfo.icns',
   'DATA'),
  ('img/vs_icns/A simple and minimalistic image icon featuring the letters "V" '
   'and "S" in a very small size that pertains to a Mac OS system lock '
   'checker. The design should be sleek and modern, emphasizing security and '
   'compatibility with Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/A '
   'simple and minimalistic image icon featuring the letters "V" and "S" in a '
   'very small size that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   'DATA'),
  ('img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg',
   'DATA'),
  ('img/vs_icns/checkADE2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/checkADE2.icns',
   'DATA'),
  ('img/vs_icns/outlook.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/outlook.icns',
   'DATA'),
  ('img/vs_icns/vs1.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs1.png',
   'DATA'),
  ('img/vs_icns/vs2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs2.png',
   'DATA'),
  ('img/vs_icns/vs_ade.jpeg',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_ade.jpeg',
   'DATA'),
  ('img/vs_icns/vs_ade.webp',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_ade.webp',
   'DATA'),
  ('img/vs_icns/vs_ade2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_ade2.png',
   'DATA'),
  ('img/vs_icns/vs_ade4.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_ade4.png',
   'DATA'),
  ('img/vs_icns/vs_ade_checker.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_ade_checker.png',
   'DATA'),
  ('img/vs_icns/vs_mac_tool_v2.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_mac_tool_v2.icns',
   'DATA'),
  ('img/vs_icns/vs_mac_tool_v2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_mac_tool_v2.png',
   'DATA'),
  ('img/vs_icns/vs_mac_tool_v2_rounded.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vs_mac_tool_v2_rounded.png',
   'DATA'),
  ('img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design '
   'should be sleek and modern, emphasizing security and compatibility with '
   'Mac OS..png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vsed '
   'that pertains to a Mac OS system lock checker. The design should be sleek '
   'and modern, emphasizing security and compatibility with Mac OS..png',
   'DATA'),
  ('img/vs_icns/vsmactool.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vs_icns/vsmactool.png',
   'DATA'),
  ('img/vsmactool.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vsmactool.icns',
   'DATA'),
  ('img/vsmactool.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/vsmactool.png',
   'DATA'),
  ('img/xxx/GenericAirDiskIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/GenericAirDiskIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarDeleteIcon.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/ToolbarDeleteIcon.icns',
   'DATA'),
  ('img/xxx/ToolbarInfo.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/ToolbarInfo.icns',
   'DATA'),
  ('img/xxx/bluetooth.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/bluetooth.png',
   'DATA'),
  ('img/xxx/bluetooth2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/bluetooth2.png',
   'DATA'),
  ('img/xxx/checkADE.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/checkADE.icns',
   'DATA'),
  ('img/xxx/checkADE.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/checkADE.png',
   'DATA'),
  ('img/xxx/checkADE2.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/checkADE2.png',
   'DATA'),
  ('img/xxx/clear.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/clear.png',
   'DATA'),
  ('img/xxx/device.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/device.icns',
   'DATA'),
  ('img/xxx/panda.icns',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/panda.icns',
   'DATA'),
  ('img/xxx/panda.ico',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/panda.ico',
   'DATA'),
  ('img/xxx/xxx.png',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/img/xxx/xxx.png',
   'DATA'),
  ('password_dialog.py',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/password_dialog.py',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info/RECORD',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/importlib_metadata-8.7.0.dist-info/RECORD',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info/INSTALLER',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/importlib_metadata-8.7.0.dist-info/INSTALLER',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info/WHEEL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/importlib_metadata-8.7.0.dist-info/WHEEL',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info/top_level.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/importlib_metadata-8.7.0.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info/METADATA',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/importlib_metadata-8.7.0.dist-info/METADATA',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info/licenses/LICENSE',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/importlib_metadata-8.7.0.dist-info/licenses/LICENSE',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('keyring-25.6.0.dist-info/entry_points.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/entry_points.txt',
   'DATA'),
  ('keyring-25.6.0.dist-info/RECORD',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/RECORD',
   'DATA'),
  ('keyring-25.6.0.dist-info/REQUESTED',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/REQUESTED',
   'DATA'),
  ('keyring-25.6.0.dist-info/WHEEL',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/WHEEL',
   'DATA'),
  ('keyring-25.6.0.dist-info/METADATA',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/METADATA',
   'DATA'),
  ('keyring-25.6.0.dist-info/INSTALLER',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/INSTALLER',
   'DATA'),
  ('keyring-25.6.0.dist-info/top_level.txt',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/top_level.txt',
   'DATA'),
  ('keyring-25.6.0.dist-info/LICENSE',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/keyring-25.6.0.dist-info/LICENSE',
   'DATA'),
  ('Python3', 'Python3.framework/Versions/3.9/Python3', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools/base_library.zip',
   'DATA'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.3.dylib', 'PIL/.dylibs/libopenjp2.2.5.3.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libz.1.3.1.zlib-ng.dylib',
   'PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libXau.6.dylib', 'PIL/.dylibs/libXau.6.dylib', 'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('Python3.framework/Python3', 'Versions/Current/Python3', 'SYMLINK'),
  ('Python3.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python3.framework/Versions/3.9/Resources/Info.plist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Info.plist',
   'DATA'),
  ('Python3.framework/Versions/Current', '3.9', 'SYMLINK')],)
