('/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
 'Mac Tools Fixed/VS Mac Tools Fixed.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools Fixed/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools Fixed/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools Fixed/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools Fixed/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/build/VS '
   'Mac Tools Fixed/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('vs_mac_tools_v2.1.0_fixed',
   '/Users/<USER>/vs-mac-coding/01_Underway/vs_mac_tools_v2.1.0/vs_mac_tools_v2.1.0_fixed.py',
   'PYSOURCE')],
 'Python3',
 True,
 False,
 False,
 [],
 'arm64',
 None,
 None)
