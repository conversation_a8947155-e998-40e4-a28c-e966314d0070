<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/__boot__.py</key>
		<data>
		I3vYvTMahwVE4DDD+ooSILOrpAQ=
		</data>
		<key>Resources/__error__.sh</key>
		<data>
		jiMXHe2/gGu9p3+O3RpqVGbONRE=
		</data>
		<key>Resources/img/.DS_Store</key>
		<data>
		rKS4zC0AqBPOFCsQiBeFC9llRpI=
		</data>
		<key>Resources/img/123.webp</key>
		<data>
		tU9ln24m/WldMzrodXCCV+zrzeE=
		</data>
		<key>Resources/img/bluetooth.icns</key>
		<data>
		BWQueLjvWPlbIq/Kh/iUbQpap6s=
		</data>
		<key>Resources/img/device.icns</key>
		<data>
		sV33xFn17BTEvfFLUBfa2Yv1sxY=
		</data>
		<key>Resources/img/erase.png</key>
		<data>
		gBENmg8aOCjcY+1IYvKYPIcz/z0=
		</data>
		<key>Resources/img/exit.png</key>
		<data>
		VWk+nE0rFweKNEjgXsZAgVd9rHI=
		</data>
		<key>Resources/img/findmy.icns</key>
		<data>
		EzmzWjuyERvTr2fQDimaOfU3Iqw=
		</data>
		<key>Resources/img/shutdown.png</key>
		<data>
		K0UjnvC4DqC907tryPgh+wfMy/0=
		</data>
		<key>Resources/img/sysinfo.icns</key>
		<data>
		Ltqqxr6FZCqK7Arl2g1zyZe3mCQ=
		</data>
		<key>Resources/img/vs_ade_checker.png</key>
		<data>
		Acz5YwpStVTXr8Jtfzn5pQKdKg4=
		</data>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		8SU9TvCuDu5FePRUdqXMkButmQA=
		</data>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<data>
		ldkboZcNMcgtxMqVzFijk+UAV4s=
		</data>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<data>
		JFvir4nfiG5ijDbsAcYQx5/aljU=
		</data>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<data>
		OwGsAniYvxO5Qk3uKKBarLJ1XzU=
		</data>
		<key>Resources/img/vs_icns/vs1.png</key>
		<data>
		WTy3qC0l5ptb5QEORcAlIreVZG8=
		</data>
		<key>Resources/img/vs_icns/vs2.png</key>
		<data>
		fN+Ic+xtw43hAA2OYq4XMWw60oQ=
		</data>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<data>
		XWVuYXsulSYsBi6hS/M9yWJxTwY=
		</data>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<data>
		Csp0Pn/Bo31hLGWXKSXAnj4PqR0=
		</data>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<data>
		rBNRUBIJKhuwT3L4ycOkP9De1Ew=
		</data>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<data>
		3w08zKeFThN4uhnlb/nlhXnxFuo=
		</data>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		e29Oba/lrQE1Wezk7M++DzQGbhc=
		</data>
		<key>Resources/img/vs_mac_tool_v2.icns</key>
		<data>
		6DLidyRNkn4oTIyC6LI0fS/sDMM=
		</data>
		<key>Resources/img/vs_mac_tool_v2.png</key>
		<data>
		tm8iNmkmZ8VXl/C4cczT2Mh3RD0=
		</data>
		<key>Resources/img/vs_mac_tool_v2_rounded.png</key>
		<data>
		wAMt3GxURS5hENvaxquEIyxAjdE=
		</data>
		<key>Resources/img/vsmactool.png</key>
		<data>
		gssccyBWyzZKOgzo4SDNHdgnYgo=
		</data>
		<key>Resources/img/xxx/.DS_Store</key>
		<data>
		3y++sUAKzaCQmjLBz2v0kvESHgc=
		</data>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<data>
		oBD4j/438pcF8UfpEu8/M3JP/X8=
		</data>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<data>
		DgyO1FNbP2AQLgHJdfq3BLYh2Vo=
		</data>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<data>
		UqlH76EyJ99OUmMBapgim5ct8fU=
		</data>
		<key>Resources/img/xxx/bluetooth.png</key>
		<data>
		9ldniYDf5s5W2rBtESN4G9Q15II=
		</data>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<data>
		boiqyT6aJvpi4JNeYXWVP0JHLNQ=
		</data>
		<key>Resources/img/xxx/checkADE.icns</key>
		<data>
		KBz6o1IJg9OplEnKhcHyPj/T4Vw=
		</data>
		<key>Resources/img/xxx/checkADE.png</key>
		<data>
		lQIica3CIebx69ddnBk4tn9b06k=
		</data>
		<key>Resources/img/xxx/checkADE2.png</key>
		<data>
		eeCm4B12OpiZAoTfNHGtmY7pAro=
		</data>
		<key>Resources/img/xxx/clear.png</key>
		<data>
		ALKnMwBx3+0JyClpaiw6/LZKT/Q=
		</data>
		<key>Resources/img/xxx/device.icns</key>
		<data>
		gFaGbZ06QBxXraWQvzdyyDGGj0E=
		</data>
		<key>Resources/img/xxx/panda.icns</key>
		<data>
		IbLGCBrdrS+78zbZMTa9U0dm6CE=
		</data>
		<key>Resources/img/xxx/panda.ico</key>
		<data>
		QFalV5j9coD4nKk4mAHrAGFDAM4=
		</data>
		<key>Resources/img/xxx/xxx.png</key>
		<data>
		QHrz8+tr6hEUo/qttHsBz6W8/pw=
		</data>
		<key>Resources/include/Headers/pyconfig.h</key>
		<data>
		yfIU5Y+h3yLwgZa1+IR18UYRhPc=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libXau.6.dylib</key>
		<data>
		cJ/lQjwsUFgdDhsPTScC/+ExYq4=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libbrotlicommon.1.1.0.dylib</key>
		<data>
		S2/qsWuWmIDuUsavTm0/p2LcOzo=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libbrotlidec.1.1.0.dylib</key>
		<data>
		wv40HvrtrWXpuOq9M9sqiV24F2w=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libfreetype.6.dylib</key>
		<data>
		MJc03N+SnLDvSLqEpWO+afjK22o=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libharfbuzz.0.dylib</key>
		<data>
		5d22/sGPSpgZkSryZ4fkgdXNrX0=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libjpeg.62.4.0.dylib</key>
		<data>
		4OwNpxfcV4R3bOwV32+nxStezA8=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/liblcms2.2.dylib</key>
		<data>
		xXNn4PjRC8GKVDhLDJkxSWsoz14=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/liblzma.5.dylib</key>
		<data>
		Ig2xXADVzhL1oS/RiFiOEQNojQQ=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libopenjp2.2.5.3.dylib</key>
		<data>
		s+4qPbNhDIAFmebMXs0NUTg/NKM=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libpng16.16.dylib</key>
		<data>
		C4eiFOH9M4kcbM2cSWVVFLexA1U=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libsharpyuv.0.dylib</key>
		<data>
		pjU5TdwX1wOQHb8pKIP4/Z2KwzQ=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libtiff.6.dylib</key>
		<data>
		3aBLpABcXXcvH551I9U2NaNRF9c=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libwebp.7.dylib</key>
		<data>
		lW+RguPXucWP+JsA8r7vi7uUbNs=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libwebpdemux.2.dylib</key>
		<data>
		NLT/6LnLhJdvPJVmNDIE+2cRIUY=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libwebpmux.3.dylib</key>
		<data>
		ylHdgqxN/+rFRoXXu2PhkHXNFV0=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libxcb.1.1.0.dylib</key>
		<data>
		m9biMZkiH7oyk55Q2Q8k6WVoDjs=
		</data>
		<key>Resources/lib/python3.9/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</key>
		<data>
		uzWkWdb7oGZmR2foom4MgQj0erQ=
		</data>
		<key>Resources/lib/python3.9/PIL/AvifImagePlugin.py</key>
		<data>
		HY22qYB9PXRIfUFj6wSMqZu+vDQ=
		</data>
		<key>Resources/lib/python3.9/PIL/BdfFontFile.py</key>
		<data>
		34X90VlSRLJbv19v+0dMxMK1uwE=
		</data>
		<key>Resources/lib/python3.9/PIL/BlpImagePlugin.py</key>
		<data>
		SALk5dKTs7ivauUwvif1by9v0xs=
		</data>
		<key>Resources/lib/python3.9/PIL/BmpImagePlugin.py</key>
		<data>
		9Utn8PEs7yWu6rc7xKmdJbpjiv0=
		</data>
		<key>Resources/lib/python3.9/PIL/BufrStubImagePlugin.py</key>
		<data>
		nD8/iLuelX5DexB5M28SONb2qyc=
		</data>
		<key>Resources/lib/python3.9/PIL/ContainerIO.py</key>
		<data>
		FAatcHDYl4Xn8a6maEwhOuLhD3Q=
		</data>
		<key>Resources/lib/python3.9/PIL/CurImagePlugin.py</key>
		<data>
		PfPxHSuXl5NLZU37L6zyk7W97Q0=
		</data>
		<key>Resources/lib/python3.9/PIL/DcxImagePlugin.py</key>
		<data>
		CrtkoAcwWW/X8/QMs6XC1NGTITU=
		</data>
		<key>Resources/lib/python3.9/PIL/DdsImagePlugin.py</key>
		<data>
		ytjjuuvxEjxr18LhOOYinZ/OY8U=
		</data>
		<key>Resources/lib/python3.9/PIL/EpsImagePlugin.py</key>
		<data>
		zKnnKzVGhKO9dFqp3vfDuTHXGUU=
		</data>
		<key>Resources/lib/python3.9/PIL/ExifTags.py</key>
		<data>
		aUR2fPb0L5j+A7w2rbIIxSrJCpw=
		</data>
		<key>Resources/lib/python3.9/PIL/FitsImagePlugin.py</key>
		<data>
		d1EttNGDdF+8EQq8ZVbnpvEP4UU=
		</data>
		<key>Resources/lib/python3.9/PIL/FliImagePlugin.py</key>
		<data>
		vlWgFKe1P3IFJ3jBLcktDLhgyhk=
		</data>
		<key>Resources/lib/python3.9/PIL/FontFile.py</key>
		<data>
		3cuhcOEHO6gegisGASpWeATm/1g=
		</data>
		<key>Resources/lib/python3.9/PIL/FpxImagePlugin.py</key>
		<data>
		pP06z20lTftbMRX7SYg5zsR6CFQ=
		</data>
		<key>Resources/lib/python3.9/PIL/FtexImagePlugin.py</key>
		<data>
		OdcdJ+rfBT9iTBJsgDDz8ubXqlU=
		</data>
		<key>Resources/lib/python3.9/PIL/GbrImagePlugin.py</key>
		<data>
		19q6ACaPrNc0DbT1RaENNRcS8Yk=
		</data>
		<key>Resources/lib/python3.9/PIL/GdImageFile.py</key>
		<data>
		4Wh7adBlqtinNFfwkabkTH2hS7g=
		</data>
		<key>Resources/lib/python3.9/PIL/GifImagePlugin.py</key>
		<data>
		cKQ60J3/IEjRrvQK1FAJgdL0bRs=
		</data>
		<key>Resources/lib/python3.9/PIL/GimpGradientFile.py</key>
		<data>
		p61eYlYc1uSiKAVlPFn50T7HWUg=
		</data>
		<key>Resources/lib/python3.9/PIL/GimpPaletteFile.py</key>
		<data>
		KdUZGEviU5vW1j/i5ebuQTPfS8s=
		</data>
		<key>Resources/lib/python3.9/PIL/GribStubImagePlugin.py</key>
		<data>
		OhliEz7G5E7XRvbNWXXhnMHwIWw=
		</data>
		<key>Resources/lib/python3.9/PIL/Hdf5StubImagePlugin.py</key>
		<data>
		eHaiZbqSjj2pHQ1tlIPClbl2YUU=
		</data>
		<key>Resources/lib/python3.9/PIL/IcnsImagePlugin.py</key>
		<data>
		c2z2BeI5YtwSNIW+/HZ9nb8Y9VQ=
		</data>
		<key>Resources/lib/python3.9/PIL/IcoImagePlugin.py</key>
		<data>
		ZrtHQdDSSmlamIJFz0WR0OUuVhc=
		</data>
		<key>Resources/lib/python3.9/PIL/ImImagePlugin.py</key>
		<data>
		HAmyL7m2Azvm/9Fu7PDdIfoDO0s=
		</data>
		<key>Resources/lib/python3.9/PIL/Image.py</key>
		<data>
		O49tq3eQTEIo4WOJtjxQxFluQqQ=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageChops.py</key>
		<data>
		VUtAxP8cwP7WoGKh6hqL09r9F1s=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageCms.py</key>
		<data>
		yHMNLBt0TecNgPso7wOe/KuXkoA=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageColor.py</key>
		<data>
		7XjxfBRR7QGt0+Pa/vUzw3PIi7A=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageDraw.py</key>
		<data>
		Q7ecWt5epgb2FbN4/8Yj1epJB1I=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageDraw2.py</key>
		<data>
		/UauigPiUhG0yTX47gkxkX1zCOk=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageEnhance.py</key>
		<data>
		8l9lqtuRXl5yn8etEtKVZXI7Tsg=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageFile.py</key>
		<data>
		CQs2QCI3epv/75xd8GakKZ5jFDg=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageFilter.py</key>
		<data>
		Qww1aVPV3CrosMeM3H5trxTgcZw=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageFont.py</key>
		<data>
		WA/fUUcLzw2Zbxtq+fQ9OasvXAs=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageGrab.py</key>
		<data>
		4+bBUzeO9qojVfei9jnr2K+ioVA=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageMath.py</key>
		<data>
		yWjnbNWNEAy5tdKCel6LoOoXNEQ=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageMode.py</key>
		<data>
		hIGWpXy1gm6PhIXPDVmrVo5XZJ8=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageMorph.py</key>
		<data>
		Ra6gHuCvVNkq5mPZaf6XSP01LzY=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageOps.py</key>
		<data>
		S8xmfSIATF4rXXq7xs/E4alnLXk=
		</data>
		<key>Resources/lib/python3.9/PIL/ImagePalette.py</key>
		<data>
		lhc3M973I+ZIMhWx9dpBurN6YYo=
		</data>
		<key>Resources/lib/python3.9/PIL/ImagePath.py</key>
		<data>
		nbqX54XTJKzq5x63svEbrsp9aMc=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageQt.py</key>
		<data>
		rFVQqufGJLGHX7sMat3bVEVZR80=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageSequence.py</key>
		<data>
		GkRBTI2xfOUmq9YAKdIVdAv+Tnw=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageShow.py</key>
		<data>
		byiEdNtHvzwG62cLaIjAPohoqVc=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageStat.py</key>
		<data>
		K5rcNhvB+6IW4fRlA1a5XnZKZds=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageTk.py</key>
		<data>
		1dGQ1gsW30rgxmPOdNMg74ovNcY=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageTransform.py</key>
		<data>
		+5ZxzhS4DOmWAzDs9By/GZKZC+g=
		</data>
		<key>Resources/lib/python3.9/PIL/ImageWin.py</key>
		<data>
		g0Ie7ppsrN894nl3I3foqsw2MN8=
		</data>
		<key>Resources/lib/python3.9/PIL/ImtImagePlugin.py</key>
		<data>
		ygXXr2oMWM0s3Ny9yxTh2hhliog=
		</data>
		<key>Resources/lib/python3.9/PIL/IptcImagePlugin.py</key>
		<data>
		ZlXAvFIeBxJJ6jG6bvQf1xYuwAg=
		</data>
		<key>Resources/lib/python3.9/PIL/Jpeg2KImagePlugin.py</key>
		<data>
		vUSHt+qpGlc39QClT+Iq5txyBl0=
		</data>
		<key>Resources/lib/python3.9/PIL/JpegImagePlugin.py</key>
		<data>
		CH7WoEGLyzLfhekADxW3NSDNyPY=
		</data>
		<key>Resources/lib/python3.9/PIL/JpegPresets.py</key>
		<data>
		9TDwQ5UBcqe8X9gstYq31Yw7Q2g=
		</data>
		<key>Resources/lib/python3.9/PIL/McIdasImagePlugin.py</key>
		<data>
		3UxCaT4oeN2fgisoLTGgDrd9kLE=
		</data>
		<key>Resources/lib/python3.9/PIL/MicImagePlugin.py</key>
		<data>
		aF9iq77UR/4A/1va7ZOu8p+hlIY=
		</data>
		<key>Resources/lib/python3.9/PIL/MpegImagePlugin.py</key>
		<data>
		fF1hQL0N+EuijvoTWHQJWOCjKf4=
		</data>
		<key>Resources/lib/python3.9/PIL/MpoImagePlugin.py</key>
		<data>
		QnHSSymhuCaIt8pCV7UTyIJxynU=
		</data>
		<key>Resources/lib/python3.9/PIL/MspImagePlugin.py</key>
		<data>
		5lATBZT2sfPfQeyH8mRBpBWIV1w=
		</data>
		<key>Resources/lib/python3.9/PIL/PSDraw.py</key>
		<data>
		1Z/J+bJsj6F7Iy9/CKk78OXXGUM=
		</data>
		<key>Resources/lib/python3.9/PIL/PaletteFile.py</key>
		<data>
		5+85PiLpJVSz6ysUGvjwwAobOrY=
		</data>
		<key>Resources/lib/python3.9/PIL/PalmImagePlugin.py</key>
		<data>
		bHCLS9NVvaZLa44WbDCiWvy3Ux0=
		</data>
		<key>Resources/lib/python3.9/PIL/PcdImagePlugin.py</key>
		<data>
		rvP1lggpQpdT0yYidPBx/xVtXdU=
		</data>
		<key>Resources/lib/python3.9/PIL/PcfFontFile.py</key>
		<data>
		QJWlI4Iq/SGZz/UxK75zROcLjQU=
		</data>
		<key>Resources/lib/python3.9/PIL/PcxImagePlugin.py</key>
		<data>
		ksNtGrwlM6jJMO/4gFtJGhIdFhc=
		</data>
		<key>Resources/lib/python3.9/PIL/PdfImagePlugin.py</key>
		<data>
		UA08B636kLCkrbSNH4pPRLY4scU=
		</data>
		<key>Resources/lib/python3.9/PIL/PdfParser.py</key>
		<data>
		fpETOhsScDq7SAd1+jzdS55qGoE=
		</data>
		<key>Resources/lib/python3.9/PIL/PixarImagePlugin.py</key>
		<data>
		xr/MNbeoTa+LOF31RUGo0D2Yfmg=
		</data>
		<key>Resources/lib/python3.9/PIL/PngImagePlugin.py</key>
		<data>
		C8PitpierwAMaRptKniG6qNDeNM=
		</data>
		<key>Resources/lib/python3.9/PIL/PpmImagePlugin.py</key>
		<data>
		y0+1684bgb5yiu7I882qjHLmtuw=
		</data>
		<key>Resources/lib/python3.9/PIL/PsdImagePlugin.py</key>
		<data>
		H/7j0+2fjIwhR4ey8oNEMqx7D1s=
		</data>
		<key>Resources/lib/python3.9/PIL/QoiImagePlugin.py</key>
		<data>
		HZ1nNBo4NqeYjFl6TAUTLoxKW/A=
		</data>
		<key>Resources/lib/python3.9/PIL/SgiImagePlugin.py</key>
		<data>
		hlZzxoAiSn3z7K4zTce1rxqajuk=
		</data>
		<key>Resources/lib/python3.9/PIL/SpiderImagePlugin.py</key>
		<data>
		D83Oiwt3h1X0b2FM+lrSPlvDZuI=
		</data>
		<key>Resources/lib/python3.9/PIL/SunImagePlugin.py</key>
		<data>
		T56qhS0i6Bo4m2IjFQ+xbKDDQXU=
		</data>
		<key>Resources/lib/python3.9/PIL/TarIO.py</key>
		<data>
		Clv5sk8SyI/6wrlNfzXFBwTXcwk=
		</data>
		<key>Resources/lib/python3.9/PIL/TgaImagePlugin.py</key>
		<data>
		AIuwCE6UtjS/glXSeQibyUUh3ak=
		</data>
		<key>Resources/lib/python3.9/PIL/TiffImagePlugin.py</key>
		<data>
		ThV1y32wa4G+7CJWe7/tN4sv8Os=
		</data>
		<key>Resources/lib/python3.9/PIL/TiffTags.py</key>
		<data>
		2Z/yFrVgUGpEYH4iWdAuoC4UYeY=
		</data>
		<key>Resources/lib/python3.9/PIL/WalImageFile.py</key>
		<data>
		8KgHbI3o4rAO2X+ESDQRiUDmMG4=
		</data>
		<key>Resources/lib/python3.9/PIL/WebPImagePlugin.py</key>
		<data>
		03HPkR5DVAc4bsjHtN2TfMSE0wg=
		</data>
		<key>Resources/lib/python3.9/PIL/WmfImagePlugin.py</key>
		<data>
		K3csG3r9UlCgdDdPZBzbYNu2uao=
		</data>
		<key>Resources/lib/python3.9/PIL/XVThumbImagePlugin.py</key>
		<data>
		fxXkY9c4J1DxZ74DokK7rwbAUfY=
		</data>
		<key>Resources/lib/python3.9/PIL/XbmImagePlugin.py</key>
		<data>
		bb7d7BPT/eNEZKv8uMlY2qS6w/E=
		</data>
		<key>Resources/lib/python3.9/PIL/XpmImagePlugin.py</key>
		<data>
		ce55WsnA26fYvEYdboN5z6fFu1I=
		</data>
		<key>Resources/lib/python3.9/PIL/__init__.py</key>
		<data>
		49BjUkryCH/NU9XCIJGT0QQQtJw=
		</data>
		<key>Resources/lib/python3.9/PIL/__main__.py</key>
		<data>
		RZ7iCPhrB+IkxjRWIxA66GGMHjQ=
		</data>
		<key>Resources/lib/python3.9/PIL/_avif.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/lib/python3.9/PIL/_binary.py</key>
		<data>
		Xh10GE1pTHiTvMggE1skUDXC/5E=
		</data>
		<key>Resources/lib/python3.9/PIL/_deprecate.py</key>
		<data>
		Es5FuOwnf5y9Gy9Ktis9GQbc8xc=
		</data>
		<key>Resources/lib/python3.9/PIL/_imaging.cpython-39-darwin.so</key>
		<data>
		GAdmwaP8uxma1hfzFrI8FeJLOwY=
		</data>
		<key>Resources/lib/python3.9/PIL/_imaging.pyi</key>
		<data>
		ytlEs9K8ZTJhjhzZ4YYdqhY5euo=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingcms.cpython-39-darwin.so</key>
		<data>
		ou18KOSbXZT88maloHxrGOBP34E=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingcms.pyi</key>
		<data>
		lpLLyFGYfeDqJoKwvb+AAZ2THz0=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingft.cpython-39-darwin.so</key>
		<data>
		ZT162zRRooaT5IZZjz9RISftyDo=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingft.pyi</key>
		<data>
		BxJj11RWU/Tc4rsrOaNmIDN7B3I=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingmath.cpython-39-darwin.so</key>
		<data>
		3HStkuXJjjri21dPjJ/NTk6H98M=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingmath.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingmorph.cpython-39-darwin.so</key>
		<data>
		OvxLTyQ4zdExvCVEiOu8cSZ1U7U=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingmorph.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingtk.cpython-39-darwin.so</key>
		<data>
		TwB3D1ml9JmWSqAnK53rXMN6g3U=
		</data>
		<key>Resources/lib/python3.9/PIL/_imagingtk.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/lib/python3.9/PIL/_tkinter_finder.py</key>
		<data>
		yyxdZNdrQ54urIiGJfYXFc0CCTw=
		</data>
		<key>Resources/lib/python3.9/PIL/_typing.py</key>
		<data>
		hGsBgxXV+bMI17wDOcy+CDQM8WE=
		</data>
		<key>Resources/lib/python3.9/PIL/_util.py</key>
		<data>
		dOiC/hXrHDbnsoOw+J+o0WGg2qw=
		</data>
		<key>Resources/lib/python3.9/PIL/_version.py</key>
		<data>
		iPCjzMfv1ajMgTU0s+P82ncs1Fk=
		</data>
		<key>Resources/lib/python3.9/PIL/_webp.cpython-39-darwin.so</key>
		<data>
		ny2f/t+X0xQduI0NEZNxqUyzEts=
		</data>
		<key>Resources/lib/python3.9/PIL/_webp.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/lib/python3.9/PIL/features.py</key>
		<data>
		zAV/O3uGO3ro6BJBRClzSn8FWW8=
		</data>
		<key>Resources/lib/python3.9/PIL/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/lib/python3.9/PIL/report.py</key>
		<data>
		W3HeUe8v91YeUhwXPRXf7DZvUG4=
		</data>
		<key>Resources/lib/python3.9/csv.py</key>
		<data>
		rLLmKHVXh96QxVYOXFMb8dRSX5U=
		</data>
		<key>Resources/lib/python3.9/customtkinter/__init__.py</key>
		<data>
		Wln+Qw0tEjfEuvROIKJm7gguSEs=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/.DS_Store</key>
		<data>
		RQwY3zFldBK3lGiMscrOXb9ejvo=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<data>
		INBhs7dCz6MeX7yGLTT1V1NO/b8=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<data>
		/dyLHGiO87rtDVpGq/XwHw7a8Cs=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<data>
		hNECSIc4sOu8elCHlz7/vVTJW9U=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/icons/.DS_Store</key>
		<data>
		smwXSpjmVksOYOLpm8eOZJC19Co=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<data>
		s2ExZOWH0JwFLDTM3E1E2sT/ROI=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/themes/blue.json</key>
		<data>
		Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/themes/dark-blue.json</key>
		<data>
		+evgfnnhRvedyIp/+JQsDkMEnw0=
		</data>
		<key>Resources/lib/python3.9/customtkinter/assets/themes/green.json</key>
		<data>
		TwB32rbJhqZKuTkmMAJMsJdyseg=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/__init__.py</key>
		<data>
		AXewfnqN2cPZWrlNUOH0rZhgE7A=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/ctk_input_dialog.py</key>
		<data>
		qSCFzm7g9oLaNqpIJMcLjHf7Bc4=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/ctk_tk.py</key>
		<data>
		RZn0egqCBDOEiXhq9HMpF/KRn0c=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/ctk_toplevel.py</key>
		<data>
		zdwCZ0vykVwxFvchH7u7SJXsyLU=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/__init__.py</key>
		<data>
		NLtGXvMXOGjzl+HX9s84CV7gFwA=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/appearance_mode/__init__.py</key>
		<data>
		WualDnHMf0Wdr1nGiXFo3sf1Q0Y=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/appearance_mode/appearance_mode_base_class.py</key>
		<data>
		67qSTVWF6ipthpMxiQ/JOUIr2rw=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/appearance_mode/appearance_mode_tracker.py</key>
		<data>
		r5uR2pG0IVovgE5AU9i3b8dfAGo=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_rendering/__init__.py</key>
		<data>
		+o2+YODiaiuggPPrb5Cl0vGp/Oo=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_rendering/ctk_canvas.py</key>
		<data>
		xn7Sx4zGdnTxI4GHtdXVETtVm78=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_rendering/draw_engine.py</key>
		<data>
		ojZuYVXjCkIgcLsohWog5oM1CN0=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_widget_classes/__init__.py</key>
		<data>
		at5fLm39WwpKIVSGQ4CatYcbGpk=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_widget_classes/ctk_base_class.py</key>
		<data>
		3r+t6ku1t4rv2CYmhJYzek9192M=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_widget_classes/dropdown_menu.py</key>
		<data>
		YLCnwzZUoLDGhiIVmY1aNF9J+Mk=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_button.py</key>
		<data>
		hL7fPuTeOE8JX/nCmF1agDYdSQU=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_checkbox.py</key>
		<data>
		SKY9XY8AYnVfnY6Xt742sBbkCzY=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_combobox.py</key>
		<data>
		0R1YvJ9PfAXy9bqqyHxeyFHdbjA=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_entry.py</key>
		<data>
		1elVPws9ZuUIXDghjd/lE1xP1fo=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_frame.py</key>
		<data>
		68Qx4SXoqMrhE6u5DHT2mEjfeF0=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_label.py</key>
		<data>
		WkiWnC7OVjXd2H9D6f+kbZlCzbA=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_optionmenu.py</key>
		<data>
		kKikSLPv5f9kH3a1w9yIkMOWKp8=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_progressbar.py</key>
		<data>
		GtCBlk0WUEmYJJqqRDl867NMz+o=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_radiobutton.py</key>
		<data>
		F3vvOznnrSdIQi5lxNwxZCPVhH0=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_scrollable_frame.py</key>
		<data>
		lX0RUgyc7OU3jaB4YTq2ls/7FxM=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_scrollbar.py</key>
		<data>
		f8NisTav3ahbJ6IaLZx2edgJNxQ=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_segmented_button.py</key>
		<data>
		DZBt5jqwZ/sIa6gMqdUBOsYgvtE=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_slider.py</key>
		<data>
		SWtK8a05rMVUshb8gcf1s9/GXYM=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_switch.py</key>
		<data>
		oFmeJTKdwVw9lURm7K9zsen3pxA=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_tabview.py</key>
		<data>
		YAYwjG6LJQw3wOxSpH+XMHVOcG8=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_textbox.py</key>
		<data>
		vVDQCAh9Pnjq6qOmHVu4qxCm/co=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/font/__init__.py</key>
		<data>
		M7kEVJFHYh4wCdun8tX/SWPZOa8=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/font/ctk_font.py</key>
		<data>
		f83WvnTzAklSbVSvPwsGykC3YI8=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/font/font_manager.py</key>
		<data>
		HiGTReaV/YEWFcX9fzdDi/xRkcs=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/image/__init__.py</key>
		<data>
		m780kLv9afgV0QQwheBjmtOFykc=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/image/ctk_image.py</key>
		<data>
		yiHJo2Rgm7NKk95zLYLlobYxYvs=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/scaling/__init__.py</key>
		<data>
		npm5iUcMgYK2XzKFeCOrekteQQw=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/scaling/scaling_base_class.py</key>
		<data>
		gLpwMzYjBP3CLztbHWJPDpwPxiY=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/scaling/scaling_tracker.py</key>
		<data>
		sQlAqv7+IErafejTT0cUcCiLARw=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/theme/__init__.py</key>
		<data>
		CiCr0sQnTlxvw8pAZlgPdJQ+uac=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/theme/theme_manager.py</key>
		<data>
		UVpdgFN/gIj6uifRhfT4a/h5bmo=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/utility/__init__.py</key>
		<data>
		5Yh9ARtnY0nRW+ttuAuli1j0jzc=
		</data>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/utility/utility_functions.py</key>
		<data>
		Ud9BJrZWgGxK8WjMwEizs6e4fq0=
		</data>
		<key>Resources/lib/python3.9/darkdetect/__init__.py</key>
		<data>
		cywQFimryK59A+UTsbaz4KrasHE=
		</data>
		<key>Resources/lib/python3.9/darkdetect/__main__.py</key>
		<data>
		fAifAKZPgik+PPUPIl/ERF8drQw=
		</data>
		<key>Resources/lib/python3.9/darkdetect/_dummy.py</key>
		<data>
		CQtwWaIAgAcq/r0lwmPu+O45n08=
		</data>
		<key>Resources/lib/python3.9/darkdetect/_linux_detect.py</key>
		<data>
		ClDMA4KQV46jtE9wtdESfE5nn/A=
		</data>
		<key>Resources/lib/python3.9/darkdetect/_mac_detect.py</key>
		<data>
		RrallIiyPrDOdxYvalygPi0vH+4=
		</data>
		<key>Resources/lib/python3.9/darkdetect/_windows_detect.py</key>
		<data>
		1fbI+bMAJqmvAfcKtHo6RShauiE=
		</data>
		<key>Resources/lib/python3.9/getpass.py</key>
		<data>
		HT/6zSSI9jHTzmaevCrWtQ/t1Kc=
		</data>
		<key>Resources/lib/python3.9/keyring/__init__.py</key>
		<data>
		m2YmbBObTEb7bnRgutC1H//9+1E=
		</data>
		<key>Resources/lib/python3.9/keyring/__main__.py</key>
		<data>
		9NP9XwwwRygM77FnyAWYhF/pkP8=
		</data>
		<key>Resources/lib/python3.9/keyring/backend.py</key>
		<data>
		1OdKXaD+2DrYYaKhVOJIrHx36z8=
		</data>
		<key>Resources/lib/python3.9/keyring/backend_complete.bash</key>
		<data>
		H2R/hP2m4SyEATDLn0cLp1zmhxU=
		</data>
		<key>Resources/lib/python3.9/keyring/backend_complete.zsh</key>
		<data>
		SQu0mKZy+Wy3jHY6HYdNC0l3ZF8=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/SecretService.py</key>
		<data>
		UxF4j6ANXzRUFpst16d9+58WKu8=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/Windows.py</key>
		<data>
		iEQmh9mg6kKje52lXQl/C/E9qEo=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/__init__.py</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/chainer.py</key>
		<data>
		2V9UlLVqIgSLLozkZsrF5X3bGng=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/fail.py</key>
		<data>
		26BRc9TTlu/a5TaaU7VTupgt8A4=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/kwallet.py</key>
		<data>
		8yEZudKMJL1zIAoIFe7Yz2Dy7JY=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/libsecret.py</key>
		<data>
		y9tEWbBgCDszyG9Ur8xijLXQxqk=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/macOS/__init__.py</key>
		<data>
		Fvm5xOlcMvEi8M9bwSLOR0o2fwA=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/macOS/api.py</key>
		<data>
		nHbJRYrmTez7/O8Vlj0te0TDyDI=
		</data>
		<key>Resources/lib/python3.9/keyring/backends/null.py</key>
		<data>
		aKpQxwcfrYLgpjVRyJR4V8YOfXY=
		</data>
		<key>Resources/lib/python3.9/keyring/cli.py</key>
		<data>
		5mcyCPQx8o3X/of4HpaFL7/+cUA=
		</data>
		<key>Resources/lib/python3.9/keyring/compat/__init__.py</key>
		<data>
		fSJ2DQ6D0UqpYAQ1qnH/u14UXJ8=
		</data>
		<key>Resources/lib/python3.9/keyring/compat/properties.py</key>
		<data>
		T/rQtyqPlKAHYH3RyGRGeqxgucs=
		</data>
		<key>Resources/lib/python3.9/keyring/compat/py312.py</key>
		<data>
		WnLWTNtg03Kt9UFBrXPIo1f433M=
		</data>
		<key>Resources/lib/python3.9/keyring/compat/py38.py</key>
		<data>
		iPE6uQTOOlUDlalRZ+IZL/AbItg=
		</data>
		<key>Resources/lib/python3.9/keyring/completion.py</key>
		<data>
		FHmdO0a6sPQCDVGQFXnHd9whnvo=
		</data>
		<key>Resources/lib/python3.9/keyring/core.py</key>
		<data>
		Ezf6dy/vkOZr7E2szluZB4TDpc8=
		</data>
		<key>Resources/lib/python3.9/keyring/credentials.py</key>
		<data>
		xJmgt5DCICk5IAswmzBOTG2TLLo=
		</data>
		<key>Resources/lib/python3.9/keyring/devpi_client.py</key>
		<data>
		geyX76HMmEWYO1hq7DO07Q7FiNI=
		</data>
		<key>Resources/lib/python3.9/keyring/errors.py</key>
		<data>
		P9GpfIAyGruE4HWR1ntduCw15k4=
		</data>
		<key>Resources/lib/python3.9/keyring/http.py</key>
		<data>
		CGfgFviJCR9jVurWtd+flKpqpXE=
		</data>
		<key>Resources/lib/python3.9/keyring/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/lib/python3.9/keyring/testing/__init__.py</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/lib/python3.9/keyring/testing/backend.py</key>
		<data>
		NfL/t1Rc/i9CwngV6JjsYucI3Q4=
		</data>
		<key>Resources/lib/python3.9/keyring/testing/util.py</key>
		<data>
		SrSAknM13y9xFYUz1A90K9enn5Q=
		</data>
		<key>Resources/lib/python3.9/keyring/util/__init__.py</key>
		<data>
		7G4yJTd5ohFKW56A3ILQnoIbrSM=
		</data>
		<key>Resources/lib/python3.9/keyring/util/platform_.py</key>
		<data>
		Akkn7UDUpTFKt31xDIqIhZWo/PU=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_asyncio.so</key>
		<data>
		dcjEbBLE0lZpr4CY12xZ3oXHNrY=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_bisect.so</key>
		<data>
		rMlB6WufLhsKArU4xfZ3elrXxW8=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_blake2.so</key>
		<data>
		CMqEBfs7iJ75tw/YLOttRmizoaU=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_bz2.so</key>
		<data>
		LupBGav6Pp/OZzJ+yTqwQppo+rE=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_cn.so</key>
		<data>
		JC2tRm+PZ589hGQpVNMS6vM/ZO8=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_hk.so</key>
		<data>
		7HmDykqEvdeB1x4Yk63JUNNQk/M=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_iso2022.so</key>
		<data>
		8lhrgEjgIMAqouHvO7Ftu7ylILo=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_jp.so</key>
		<data>
		RbCUvlcD3khoLffULu0K9MHbB7I=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_kr.so</key>
		<data>
		1h37IL8NPpjCG4atqtvVkTNTfrI=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_tw.so</key>
		<data>
		qJdcFd5l9iPam10p7rkdE2jv9I4=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_contextvars.so</key>
		<data>
		FXpNTmau1vsBHNeCSkU21kENyFE=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_csv.so</key>
		<data>
		/NSaQhZWFt/+jilkDEgzlFhnfR0=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_ctypes.so</key>
		<data>
		10+zm89aeOYnmaDno8nn87i2/as=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_datetime.so</key>
		<data>
		gN6xUyYcQWodO0Jnasuw85mcsrI=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_decimal.so</key>
		<data>
		7KkHVfRM8Er67D/7N6M2/6ohBGw=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_elementtree.so</key>
		<data>
		LnQ51rGZIONwyDUDRYG61lki6pE=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_hashlib.so</key>
		<data>
		qfCZ0uMDDTdoypDAEf2BROub9vo=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_heapq.so</key>
		<data>
		pqZC+W8N4PXZ5y9z/76qO6FxDJ4=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_json.so</key>
		<data>
		SXPERgMWjkd3vSaVpFz3hdyURJo=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_lzma.so</key>
		<data>
		HUbGnAGFarAVQLi7xjXgI5rpOhQ=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_md5.so</key>
		<data>
		k8jQFVKHd5u9Wo9GbsJ9zHkanRA=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_multibytecodec.so</key>
		<data>
		tRR9PRdiPx/HD6UpIB9xaNSIYMk=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_multiprocessing.so</key>
		<data>
		Dq2S7Fb8CHnCExIjzaVKFdbMBiY=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_opcode.so</key>
		<data>
		bHy5c25keGrZwBlTakrrK8Cz8g4=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_pickle.so</key>
		<data>
		1w6ytvRxbjzUxaT1Jh9OUuqrI3s=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_posixshmem.so</key>
		<data>
		qfShPtf/0UmlEtlk0OqexfimOcA=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_posixsubprocess.so</key>
		<data>
		47d8JStsRsMugp9KL60BiZjXOIg=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_queue.so</key>
		<data>
		x+wY4s4nSkPoTL8aNkMPVlwSFBs=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_random.so</key>
		<data>
		nAZRnGKApc0cYx9mozFaXIOtnRk=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_scproxy.so</key>
		<data>
		yAycvYZhpNgYLQ/nZ2GyRJiYv/E=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_sha1.so</key>
		<data>
		CmPtUfPLnhPTOlwPLN74RmWl3I0=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_sha256.so</key>
		<data>
		UASFPBUWwBfx80/CoTdXcSZeprM=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_sha3.so</key>
		<data>
		T2GIJodMuH3CqS7IcoZEDWghgYw=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_sha512.so</key>
		<data>
		AZUByakSC0XPT3AhQQT24je0KFY=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_socket.so</key>
		<data>
		MyJDzuWUGSytSEZAALeQMoX+5MQ=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_ssl.so</key>
		<data>
		8YozUEmN/CPpkpZJFDQBzJ5rCAQ=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_statistics.so</key>
		<data>
		Ag93je9thyGtXlWN5qX1TqBe4P8=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_struct.so</key>
		<data>
		6CTH0gbXOyu0X/HYEcum6QxKQkg=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/_tkinter.so</key>
		<data>
		6DuUphwoSOti8YC+lghd93eMgu4=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/array.so</key>
		<data>
		q57fiWikohrTaY8Qmh5JAXsZnmc=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/binascii.so</key>
		<data>
		bIb1Dj9/lgl8UGBbW+QawTCUhB4=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/grp.so</key>
		<data>
		es0fXdLedoVrqPxPPHfC0yhkUC4=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/math.so</key>
		<data>
		qc699vXMUofZv2mcEbgRbmQuLXA=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/mmap.so</key>
		<data>
		zvptJJTW68v8266GyePc3aSWgds=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/pyexpat.so</key>
		<data>
		j5KBHjGSTSlFkzj/p9ko3R1dloM=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/resource.so</key>
		<data>
		cTRGLNnBFkrCnh+9zrXQ4hWiV1c=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/select.so</key>
		<data>
		TJ5h+5rEEsmGJw6FNTjJj2sz704=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/termios.so</key>
		<data>
		ocaRQxaIXTyXK7SREVIeMiP4jbo=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/unicodedata.so</key>
		<data>
		6nftJMiVVGV9Sl21+uL6kbAy6+k=
		</data>
		<key>Resources/lib/python3.9/lib-dynload/zlib.so</key>
		<data>
		k8OPcRYfPw/B/LpS48/6+w2yOfE=
		</data>
		<key>Resources/lib/python3.9/pathlib.py</key>
		<data>
		eT8N7Ac9mO/G8569COkmtZVGrlM=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/__init__.py</key>
		<data>
		pZR23JQpotio4n8yh/o3ncCVAsw=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/api_tests.txt</key>
		<data>
		5kbHVTOUa3XReBiUNgr1GnGlYBw=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/__init__.py</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package-source/setup.cfg</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package-source/setup.py</key>
		<data>
		YzPI3SI98w9lIahP7jnI1NBl8EU=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip</key>
		<data>
		Ppo7Vi7IEYtwOiI8WhWVfbIQBHs=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO</key>
		<data>
		bXpnwYYTvkslWBIRG0YZ3peavCg=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt</key>
		<data>
		TJ2MjjVWRax3KqI3UIcpDte2NXs=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt</key>
		<data>
		rcg7GeeTSRscbqD9i0bNnzLlkvw=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt</key>
		<data>
		rcg7GeeTSRscbqD9i0bNnzLlkvw=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe</key>
		<data>
		rcg7GeeTSRscbqD9i0bNnzLlkvw=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg</key>
		<data>
		nUk3q6cqxlg+uWEUYFeL3QtXoCU=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_find_distributions.py</key>
		<data>
		CVpgQ1bXFyNsPUwRhPqB5RuE58I=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_integration_zope_interface.py</key>
		<data>
		kOefkG+CjQINQqEmITFENZQBfRg=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_markers.py</key>
		<data>
		cPy9DVAWGZx5I00CxSoNhKMoDnw=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_pkg_resources.py</key>
		<data>
		sajuwdZrAGOSB9LLPp1iTOK0kkM=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_resources.py</key>
		<data>
		aMg7adI9kZQLSQigZv6XBWul8jY=
		</data>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_working_set.py</key>
		<data>
		GFG7sdHW07sfzDTHzhq2Iw0dGe4=
		</data>
		<key>Resources/lib/python3.9/platform.py</key>
		<data>
		R/OnehVJzdSVo81ARA6312Sa5GU=
		</data>
		<key>Resources/lib/python3.9/queue.py</key>
		<data>
		fUCeNg0y8WEBGMSaMP28IV/qAc4=
		</data>
		<key>Resources/lib/python3.9/socket.py</key>
		<data>
		srrtvxGy7x3uVsxGJN8gClj6v1E=
		</data>
		<key>Resources/lib/python3.9/subprocess.py</key>
		<data>
		Ex0sC4kIbI8hWlnxNI/6WyhQcV8=
		</data>
		<key>Resources/lib/python3.9/threading.py</key>
		<data>
		9TR/PEGkLRFsjjIhl2C0ENu0EGk=
		</data>
		<key>Resources/lib/python3.9/tkinter/__init__.py</key>
		<data>
		0MW+CGUqz7tZTBzAYrEzVx0zZ/w=
		</data>
		<key>Resources/lib/python3.9/tkinter/__main__.py</key>
		<data>
		p3f6Ybk1seXMnsaMZvHqyKhXbuY=
		</data>
		<key>Resources/lib/python3.9/tkinter/colorchooser.py</key>
		<data>
		TnuKeJ2LdbJw89UIaqTQF3TIRTY=
		</data>
		<key>Resources/lib/python3.9/tkinter/commondialog.py</key>
		<data>
		a4EyG4xEffUSCKetuFHsT9PfOJY=
		</data>
		<key>Resources/lib/python3.9/tkinter/constants.py</key>
		<data>
		/B7xu2NqfWUwfm+fw6c5oH34luM=
		</data>
		<key>Resources/lib/python3.9/tkinter/dialog.py</key>
		<data>
		g+BVF0qNJJi+C7eqTWfZmoKWh5A=
		</data>
		<key>Resources/lib/python3.9/tkinter/dnd.py</key>
		<data>
		EwuL5jfgyqqgTMcityoNPck52TI=
		</data>
		<key>Resources/lib/python3.9/tkinter/filedialog.py</key>
		<data>
		4q2l1IlpnVIYWXqO73LZ07VJxhU=
		</data>
		<key>Resources/lib/python3.9/tkinter/font.py</key>
		<data>
		HSD8nDaD6lU+ymQe3f1W8QxD+4w=
		</data>
		<key>Resources/lib/python3.9/tkinter/messagebox.py</key>
		<data>
		VJcUvUzf8VE7IAb7I/ch8EubU00=
		</data>
		<key>Resources/lib/python3.9/tkinter/scrolledtext.py</key>
		<data>
		nrnBvmSIrJ2rO5NpFXS2JckZQwc=
		</data>
		<key>Resources/lib/python3.9/tkinter/simpledialog.py</key>
		<data>
		ne8dP+ZvTlMvwIboL2b1mJoEfAo=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/README</key>
		<data>
		zeWBu5/aaPO06CqQtjGNqBzH3Rs=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/__init__.py</key>
		<data>
		oBgYes7z6WsNBBcvpV0BOB/Oks0=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/runtktests.py</key>
		<data>
		MrAZCMWJIelT7Din9ckqF58wLqU=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/support.py</key>
		<data>
		un1n/mSt7XPmcLhZipiNFS5YqZQ=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/__init__.py</key>
		<data>
		oBgYes7z6WsNBBcvpV0BOB/Oks0=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_colorchooser.py</key>
		<data>
		oWu9ewDSooLDuMzQBWkwMUkOgNU=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_font.py</key>
		<data>
		/LgsY1TC1BudvlzRvueSlXVj4hg=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_geometry_managers.py</key>
		<data>
		sxj0SspjZcGk5ei62BaPUCMvA7k=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_images.py</key>
		<data>
		Nk4XWDcryv/R9GK679v0n871yCg=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_loadtk.py</key>
		<data>
		2ZM/FetRiQZZseW17oVJ93E5YZk=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_misc.py</key>
		<data>
		mHmQm4BEWwrKJcDnmf3pum9Vkvw=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_simpledialog.py</key>
		<data>
		1HPHBNEGDhWGdMwKu4N+xGDUiRM=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_text.py</key>
		<data>
		WV9/R2CJqT2V24ZQoQ6+JvIEmgM=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_variables.py</key>
		<data>
		8fzykfVAsT+HaviDxz8olKKI/bs=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_widgets.py</key>
		<data>
		lO/pVgs/9ApmqdMJMMDWDDvdC08=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/__init__.py</key>
		<data>
		oBgYes7z6WsNBBcvpV0BOB/Oks0=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_extensions.py</key>
		<data>
		vjCuWw4/Dh4kDcuf6fn/xJm6xsM=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_functions.py</key>
		<data>
		uH+dqYccsx1rdEhAjRO+R/Qnzdc=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_style.py</key>
		<data>
		1gQDhQLRoNsHkhWhjqIbEMjcl/w=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_widgets.py</key>
		<data>
		p3IjjUbYMOCmtxBeIGSMBMsfSus=
		</data>
		<key>Resources/lib/python3.9/tkinter/test/widget_tests.py</key>
		<data>
		ofg6mYDjGgms61oKTV0qFpNWQBo=
		</data>
		<key>Resources/lib/python3.9/tkinter/tix.py</key>
		<data>
		gLFCdBrF6wKix+LeQspBxB3PPyY=
		</data>
		<key>Resources/lib/python3.9/tkinter/ttk.py</key>
		<data>
		YjC5kvG4p6M6BFRnbYPbUR69mp4=
		</data>
		<key>Resources/lib/python3.9/xml/05-16 test.xml</key>
		<data>
		oCrSTZl90lrfp0sIezfZ0yEc0l8=
		</data>
		<key>Resources/lib/python3.9/xml/8123340.xml</key>
		<data>
		pGAOUdCdXNCVjEFxbiHXj4Np5dA=
		</data>
		<key>Resources/lib/python3.9/xml/8123456.xml</key>
		<data>
		dx/ez0+pzADCIoeErZ4xC7+4z4I=
		</data>
		<key>Resources/lib/python3.9/xml/reports (1).xml</key>
		<data>
		XbdKR+/YVse8PU3DzelmFigldr8=
		</data>
		<key>Resources/lib/python3.9/xml/reports.xml</key>
		<data>
		f8plR/Z28+YSBLAp/ubwjwrka7o=
		</data>
		<key>Resources/lib/python39.zip</key>
		<data>
		K9eNk3HhCneCszwaIMDSGH1YcV0=
		</data>
		<key>Resources/openssl.ca/cert.pem</key>
		<data>
		pozv5aCs1VeWKDdWZs+5LigONIM=
		</data>
		<key>Resources/site.pyc</key>
		<data>
		EBeYGtd9sFxUNKYLuyif8x/bW6w=
		</data>
		<key>Resources/vs_mac_tool_v2.icns</key>
		<data>
		6DLidyRNkn4oTIyC6LI0fS/sDMM=
		</data>
		<key>Resources/vs_mac_tools_v2.1.0.py</key>
		<data>
		azxBUVNy+vb1DZDucpdGjgCWM4A=
		</data>
		<key>Resources/zlib.cpython-39-darwin.so</key>
		<data>
		k8OPcRYfPw/B/LpS48/6+w2yOfE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Python3.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			8zll91v5Q/WDV/jMBUim/pmz7Yc=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.python3" and anchor apple</string>
		</dict>
		<key>MacOS/python</key>
		<dict>
			<key>cdhash</key>
			<data>
			2FAoCskPcf/B4J9Tw444cVsyMCc=
			</data>
			<key>requirement</key>
			<string>cdhash H"d850280ac90f71ffc1e09f53c38e38715b323027" or cdhash H"cc9239a9b907508689194659bad187d15ab221d5"</string>
		</dict>
		<key>Resources/__boot__.py</key>
		<dict>
			<key>hash</key>
			<data>
			I3vYvTMahwVE4DDD+ooSILOrpAQ=
			</data>
			<key>hash2</key>
			<data>
			yLy0YOHFamV3YyDlL4vAscnRGlL1K+HNUy4GNzF7RAA=
			</data>
		</dict>
		<key>Resources/__error__.sh</key>
		<dict>
			<key>hash</key>
			<data>
			jiMXHe2/gGu9p3+O3RpqVGbONRE=
			</data>
			<key>hash2</key>
			<data>
			X92yvDN0TrSkH2FT7nqBEm6WgAZJ9CS2MXV1a7IpE4o=
			</data>
		</dict>
		<key>Resources/img/123.webp</key>
		<dict>
			<key>hash</key>
			<data>
			tU9ln24m/WldMzrodXCCV+zrzeE=
			</data>
			<key>hash2</key>
			<data>
			zRbt2eaNE2k5bwMRPx6qaxe5d8M6NSKc7Qjkbrb/ODY=
			</data>
		</dict>
		<key>Resources/img/bluetooth.icns</key>
		<dict>
			<key>hash</key>
			<data>
			BWQueLjvWPlbIq/Kh/iUbQpap6s=
			</data>
			<key>hash2</key>
			<data>
			AXDfQ7PFxvX7gGQYsojZ3L4PMUU0IIMf0Vbora632Mc=
			</data>
		</dict>
		<key>Resources/img/device.icns</key>
		<dict>
			<key>hash</key>
			<data>
			sV33xFn17BTEvfFLUBfa2Yv1sxY=
			</data>
			<key>hash2</key>
			<data>
			/wu7jVkhEKGPoKTYum8zEAUTPQuFfFMXP41QTSed88g=
			</data>
		</dict>
		<key>Resources/img/erase.png</key>
		<dict>
			<key>hash</key>
			<data>
			gBENmg8aOCjcY+1IYvKYPIcz/z0=
			</data>
			<key>hash2</key>
			<data>
			DltqZK/wdHkI4bv8nRiuvrd1BdOrrb3uBTsOzFcZRZ8=
			</data>
		</dict>
		<key>Resources/img/exit.png</key>
		<dict>
			<key>hash</key>
			<data>
			VWk+nE0rFweKNEjgXsZAgVd9rHI=
			</data>
			<key>hash2</key>
			<data>
			b5d6vlIoz/sJxS4j+nsUsX46dyNReM1kJkVz+QSIJJ8=
			</data>
		</dict>
		<key>Resources/img/findmy.icns</key>
		<dict>
			<key>hash</key>
			<data>
			EzmzWjuyERvTr2fQDimaOfU3Iqw=
			</data>
			<key>hash2</key>
			<data>
			6O5TLJgtkHq7LHYtkdu2JJQppDEmUUKnPrSZurSZ3Ew=
			</data>
		</dict>
		<key>Resources/img/shutdown.png</key>
		<dict>
			<key>hash</key>
			<data>
			K0UjnvC4DqC907tryPgh+wfMy/0=
			</data>
			<key>hash2</key>
			<data>
			adYIbIXC8nbpXDrEmKQUQo+zPZY5R8nBZJbzWaxWFPc=
			</data>
		</dict>
		<key>Resources/img/sysinfo.icns</key>
		<dict>
			<key>hash</key>
			<data>
			Ltqqxr6FZCqK7Arl2g1zyZe3mCQ=
			</data>
			<key>hash2</key>
			<data>
			vZn9xKNKjSY5I0arOZQ4oRzT4WN8azNInwkwJN8dE2k=
			</data>
		</dict>
		<key>Resources/img/vs_ade_checker.png</key>
		<dict>
			<key>hash</key>
			<data>
			Acz5YwpStVTXr8Jtfzn5pQKdKg4=
			</data>
			<key>hash2</key>
			<data>
			vuFlbfGrIEV2gk+uuiuwLfAsc9VuCo4LT4bVuAJjm6U=
			</data>
		</dict>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash</key>
			<data>
			8SU9TvCuDu5FePRUdqXMkButmQA=
			</data>
			<key>hash2</key>
			<data>
			rNQktSPlSyE+VKfSgMGXqpOZTyTJd6h4JNlIUpdDoyA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<dict>
			<key>hash</key>
			<data>
			ldkboZcNMcgtxMqVzFijk+UAV4s=
			</data>
			<key>hash2</key>
			<data>
			pP6U2IyrwYxqXce/NqNnp6uZ/AyYydNiJlw13ZKwobE=
			</data>
		</dict>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<dict>
			<key>hash</key>
			<data>
			JFvir4nfiG5ijDbsAcYQx5/aljU=
			</data>
			<key>hash2</key>
			<data>
			WqaKLAzi7FB7Ewbroi4ebjBga54GoM94tWkm7qx3p90=
			</data>
		</dict>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<dict>
			<key>hash</key>
			<data>
			OwGsAniYvxO5Qk3uKKBarLJ1XzU=
			</data>
			<key>hash2</key>
			<data>
			tjT6ORXV56DHmCh07PXANHtyqJKen6F6ymXv2Z5QlMk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs1.png</key>
		<dict>
			<key>hash</key>
			<data>
			WTy3qC0l5ptb5QEORcAlIreVZG8=
			</data>
			<key>hash2</key>
			<data>
			GeEl6CBzyDu50Hu4C2j+XLVl/4jQbIC0t3SlJyK7BsA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs2.png</key>
		<dict>
			<key>hash</key>
			<data>
			fN+Ic+xtw43hAA2OYq4XMWw60oQ=
			</data>
			<key>hash2</key>
			<data>
			1ju/nmVj5Szaqn5wjFVQo4KMWcXB/CjOpYGSfMgk4Kk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<dict>
			<key>hash</key>
			<data>
			XWVuYXsulSYsBi6hS/M9yWJxTwY=
			</data>
			<key>hash2</key>
			<data>
			sTzAaJlRqYcg0y5qmy5xwuyQ4gRCnPggVcJIdWnX8i4=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<dict>
			<key>hash</key>
			<data>
			Csp0Pn/Bo31hLGWXKSXAnj4PqR0=
			</data>
			<key>hash2</key>
			<data>
			FJBSpYLuVVrMudR2GNQ0yVHsWgz64STZc5AUwJRJOOU=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<dict>
			<key>hash</key>
			<data>
			rBNRUBIJKhuwT3L4ycOkP9De1Ew=
			</data>
			<key>hash2</key>
			<data>
			He/b6Cc/DD4cLlU4K4nqPm3/r1AILuwNskrylo3JHbc=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<dict>
			<key>hash</key>
			<data>
			3w08zKeFThN4uhnlb/nlhXnxFuo=
			</data>
			<key>hash2</key>
			<data>
			3XK2IiySOO3EzgPGPNv7hpDmm7gIKTs1mkCU7SSaGAg=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash</key>
			<data>
			e29Oba/lrQE1Wezk7M++DzQGbhc=
			</data>
			<key>hash2</key>
			<data>
			NpKcDDxjT73l8z1/Nc1X0EFty0BKBNJ7s7LIbSUh3r8=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2.icns</key>
		<dict>
			<key>hash</key>
			<data>
			6DLidyRNkn4oTIyC6LI0fS/sDMM=
			</data>
			<key>hash2</key>
			<data>
			lrPqU4byxTwkOHY4msSrYvfC+sUcrU7P0gZ68v7Cn2E=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2.png</key>
		<dict>
			<key>hash</key>
			<data>
			tm8iNmkmZ8VXl/C4cczT2Mh3RD0=
			</data>
			<key>hash2</key>
			<data>
			CD+ufxpcMTBVE8LJvKzniszXAhS8xNABGEo6MHqbwPU=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2_rounded.png</key>
		<dict>
			<key>hash</key>
			<data>
			wAMt3GxURS5hENvaxquEIyxAjdE=
			</data>
			<key>hash2</key>
			<data>
			iecaM5vNBiUb+RG27Jy3/GUEgorL3Cd8XVY6gYkRcFc=
			</data>
		</dict>
		<key>Resources/img/vsmactool.png</key>
		<dict>
			<key>hash</key>
			<data>
			gssccyBWyzZKOgzo4SDNHdgnYgo=
			</data>
			<key>hash2</key>
			<data>
			jOZag2Q3yCpPYvFFXgOuK2Baf8a+HUylmWUhBDhBI6Q=
			</data>
		</dict>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<dict>
			<key>hash</key>
			<data>
			oBD4j/438pcF8UfpEu8/M3JP/X8=
			</data>
			<key>hash2</key>
			<data>
			yOfFn7nhq1viUuO2rlX5hyia0vArQ9JV/8opaAICgTA=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<dict>
			<key>hash</key>
			<data>
			DgyO1FNbP2AQLgHJdfq3BLYh2Vo=
			</data>
			<key>hash2</key>
			<data>
			7NPvIdku/VrKm7yv+ITV0DWiNJ8o/9GzDdwNyBPR31c=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<dict>
			<key>hash</key>
			<data>
			UqlH76EyJ99OUmMBapgim5ct8fU=
			</data>
			<key>hash2</key>
			<data>
			rblFkpNuBhHlkhAnZmha0YXeMvlvn59Z1WHiRV8i9oA=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth.png</key>
		<dict>
			<key>hash</key>
			<data>
			9ldniYDf5s5W2rBtESN4G9Q15II=
			</data>
			<key>hash2</key>
			<data>
			n+3wBQtqTyxoYteALhMKiZcHUdA1B07ssRXWRE88dDQ=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<dict>
			<key>hash</key>
			<data>
			boiqyT6aJvpi4JNeYXWVP0JHLNQ=
			</data>
			<key>hash2</key>
			<data>
			8IWgML4+zBqL3Is226iqcU6EUotB/sTfNaJ6SfeMef8=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.icns</key>
		<dict>
			<key>hash</key>
			<data>
			KBz6o1IJg9OplEnKhcHyPj/T4Vw=
			</data>
			<key>hash2</key>
			<data>
			1V1jeQug46T0y08D/mYq79KveXSvkHV559jV7HKjgF0=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.png</key>
		<dict>
			<key>hash</key>
			<data>
			lQIica3CIebx69ddnBk4tn9b06k=
			</data>
			<key>hash2</key>
			<data>
			XsQmYdvHwyFU0dtXw2caSJKrbExuSwrxFFwUrvUeCyU=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE2.png</key>
		<dict>
			<key>hash</key>
			<data>
			eeCm4B12OpiZAoTfNHGtmY7pAro=
			</data>
			<key>hash2</key>
			<data>
			OEJKmlGH50iAIpFiklwgppToeAxlRc93d70whg30LxA=
			</data>
		</dict>
		<key>Resources/img/xxx/clear.png</key>
		<dict>
			<key>hash</key>
			<data>
			ALKnMwBx3+0JyClpaiw6/LZKT/Q=
			</data>
			<key>hash2</key>
			<data>
			JMy6eOPBIeGPyt7CJb0POj5Gk/27KHY/7WH+EncDbgo=
			</data>
		</dict>
		<key>Resources/img/xxx/device.icns</key>
		<dict>
			<key>hash</key>
			<data>
			gFaGbZ06QBxXraWQvzdyyDGGj0E=
			</data>
			<key>hash2</key>
			<data>
			EVrTPOkfu3b6zVGgdbFJqeL4YSUjlAaHNBfGUMoNb8w=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.icns</key>
		<dict>
			<key>hash</key>
			<data>
			IbLGCBrdrS+78zbZMTa9U0dm6CE=
			</data>
			<key>hash2</key>
			<data>
			LDnYsUI7Tbe+oZvhEh+36wBZLpo3JsKIUU8tq3vcx1s=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.ico</key>
		<dict>
			<key>hash</key>
			<data>
			QFalV5j9coD4nKk4mAHrAGFDAM4=
			</data>
			<key>hash2</key>
			<data>
			8s+4K1yGSugGA3D+rfgAcSIaVxl7b1YT+GN+tiwKA1A=
			</data>
		</dict>
		<key>Resources/img/xxx/xxx.png</key>
		<dict>
			<key>hash</key>
			<data>
			QHrz8+tr6hEUo/qttHsBz6W8/pw=
			</data>
			<key>hash2</key>
			<data>
			rrsBqmDhsnewSY7OzcdCmv3LFV8XTgh4kDnyoL33s28=
			</data>
		</dict>
		<key>Resources/include/Headers/pyconfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			yfIU5Y+h3yLwgZa1+IR18UYRhPc=
			</data>
			<key>hash2</key>
			<data>
			JW1SMDwhgrVLPu1YrhjRxWQCAhjx16mXrVxzxAaK8PA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libXau.6.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			cJ/lQjwsUFgdDhsPTScC/+ExYq4=
			</data>
			<key>hash2</key>
			<data>
			ck3JGKnC26n/cqCH8GB7mnz+4pPXs7GsqKp9KZCLiDA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libbrotlicommon.1.1.0.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			S2/qsWuWmIDuUsavTm0/p2LcOzo=
			</data>
			<key>hash2</key>
			<data>
			pXLDgUNb0wpcMJnwkO4YZHLF6nGPooOO4WIH33csqt0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libbrotlidec.1.1.0.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			wv40HvrtrWXpuOq9M9sqiV24F2w=
			</data>
			<key>hash2</key>
			<data>
			qaqfBO12vSF+YqBV13wq3SBqMbRxHYXHssvYz4s3oQg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libfreetype.6.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			MJc03N+SnLDvSLqEpWO+afjK22o=
			</data>
			<key>hash2</key>
			<data>
			tomutrp1XwDsd0ZBWUN6gxd8xJBqi7mKuo6gL1E8Ry0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libharfbuzz.0.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			5d22/sGPSpgZkSryZ4fkgdXNrX0=
			</data>
			<key>hash2</key>
			<data>
			jjcBg4BjMlPVVtM+7XiL+44r1UVu3oCs9KXa0o3O6NU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			4OwNpxfcV4R3bOwV32+nxStezA8=
			</data>
			<key>hash2</key>
			<data>
			9nZIgGzHwssAjUmLDEdZvSCWPFBniGu6ZXFi+Lza1bA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			xXNn4PjRC8GKVDhLDJkxSWsoz14=
			</data>
			<key>hash2</key>
			<data>
			v5lUWOGWadiwbnFdt/WUlwP/E6rew8XkpWvTgcP73wI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/liblzma.5.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			Ig2xXADVzhL1oS/RiFiOEQNojQQ=
			</data>
			<key>hash2</key>
			<data>
			uEVyeVmjI+D6JB0hjV73v8Z/tSMHvHdSD+X72spUbsI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			s+4qPbNhDIAFmebMXs0NUTg/NKM=
			</data>
			<key>hash2</key>
			<data>
			ycp05dnl5AHaV87iB+5hynTRfDj1EryUcp8S9aVxtwo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libpng16.16.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			C4eiFOH9M4kcbM2cSWVVFLexA1U=
			</data>
			<key>hash2</key>
			<data>
			nLbglg7E2NO1LarBYSjT5w3aVu88dblbBL90a5IeNdM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			pjU5TdwX1wOQHb8pKIP4/Z2KwzQ=
			</data>
			<key>hash2</key>
			<data>
			6S1TGrMHgcXF3gGcRdQbZ2q4wpfErTnhSq0gGUwTpdo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libtiff.6.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			3aBLpABcXXcvH551I9U2NaNRF9c=
			</data>
			<key>hash2</key>
			<data>
			YcImNjUf1Zch2z0TJb8f08ZWbfuAxSe7RrfaDr+XkIo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libwebp.7.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			lW+RguPXucWP+JsA8r7vi7uUbNs=
			</data>
			<key>hash2</key>
			<data>
			e13j03zmf+ZUJ8rJu9jIrNr6hTStoahmbtiGsolPAKw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			NLT/6LnLhJdvPJVmNDIE+2cRIUY=
			</data>
			<key>hash2</key>
			<data>
			Z8SZ8gq6XlqpknJ1t4bJHN+oUCNwc2cDypQxgEF2T4w=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			ylHdgqxN/+rFRoXXu2PhkHXNFV0=
			</data>
			<key>hash2</key>
			<data>
			hg53yxohCxjTgKxANOxwzCjGxDog2w/DctwkLO4cJyE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			m9biMZkiH7oyk55Q2Q8k6WVoDjs=
			</data>
			<key>hash2</key>
			<data>
			6MrtUcPoB5UeztixePOv6b7CalwMfec/HuaVO7eGMPo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			uzWkWdb7oGZmR2foom4MgQj0erQ=
			</data>
			<key>hash2</key>
			<data>
			TT68GZvQW9EQJK2KQC9T38KpiRtnM7yrrKtAz7osdk0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/AvifImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			HY22qYB9PXRIfUFj6wSMqZu+vDQ=
			</data>
			<key>hash2</key>
			<data>
			aJ2ojydbq/Sdm8IVcxtztCsmnu+OkTAjO4DoI5tOyaQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/BdfFontFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			34X90VlSRLJbv19v+0dMxMK1uwE=
			</data>
			<key>hash2</key>
			<data>
			PhlZfIRmEfmorbhZZeSM5eebGo1Ei7fL+lR9XlfTZZA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/BlpImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			SALk5dKTs7ivauUwvif1by9v0xs=
			</data>
			<key>hash2</key>
			<data>
			Ub4vVKBEniiNBEgNizxScEpO1VKbC1w6iecWUU7T+Vs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/BmpImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			9Utn8PEs7yWu6rc7xKmdJbpjiv0=
			</data>
			<key>hash2</key>
			<data>
			Rsduui0rfnRSx2kDt8IDPLzadBaze5AreE/qvFtaFaU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/BufrStubImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			nD8/iLuelX5DexB5M28SONb2qyc=
			</data>
			<key>hash2</key>
			<data>
			JSqDhkPNPnFw0Qcz+gQJl+D/iSCFdtcLvPynshKJ4WM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ContainerIO.py</key>
		<dict>
			<key>hash</key>
			<data>
			FAatcHDYl4Xn8a6maEwhOuLhD3Q=
			</data>
			<key>hash2</key>
			<data>
			wkBqL2GDAb5fh3wrtfTGUfqioJipCl+lg2GxbjQrTZw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/CurImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			PfPxHSuXl5NLZU37L6zyk7W97Q0=
			</data>
			<key>hash2</key>
			<data>
			bICiwXZrzSONWBu4bKtshxZSNFj8su0lbDojYntEUYs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/DcxImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			CrtkoAcwWW/X8/QMs6XC1NGTITU=
			</data>
			<key>hash2</key>
			<data>
			DhqsmW7MjmnUSTGZ+Skv9hz1XeX3XoQQoAl9GWLAEEY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/DdsImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			ytjjuuvxEjxr18LhOOYinZ/OY8U=
			</data>
			<key>hash2</key>
			<data>
			lmOwMdfG31IGqOI/rgiMYoAnhBhsKlACxQj6o9DADcY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/EpsImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			zKnnKzVGhKO9dFqp3vfDuTHXGUU=
			</data>
			<key>hash2</key>
			<data>
			ROWwCv08bC/B41eMf2AFe8UW6ZH4/XQ18x12KB/aQLM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ExifTags.py</key>
		<dict>
			<key>hash</key>
			<data>
			aUR2fPb0L5j+A7w2rbIIxSrJCpw=
			</data>
			<key>hash2</key>
			<data>
			zW6kVikCosiyoCo7J7R62evD3hoxjKPchnVh8po7CZc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/FitsImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			d1EttNGDdF+8EQq8ZVbnpvEP4UU=
			</data>
			<key>hash2</key>
			<data>
			+oDJnAH113CK5qPvwz9lL81fkV1gla/tNfqLcq8zKgo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/FliImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			vlWgFKe1P3IFJ3jBLcktDLhgyhk=
			</data>
			<key>hash2</key>
			<data>
			DaWuH8f+9GihS0VVZqF1bT3uDv1Vb0VBl0chnNd82Ow=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/FontFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			3cuhcOEHO6gegisGASpWeATm/1g=
			</data>
			<key>hash2</key>
			<data>
			St7MxO5Q+oakCLWn3ZrgrtaT3wSsmAarxm8AU+G8Moc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/FpxImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			pP06z20lTftbMRX7SYg5zsR6CFQ=
			</data>
			<key>hash2</key>
			<data>
			aXfg0YdvNeJhxqh+f+f22D1NobQ8tSVCj+tpLE2PKfE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/FtexImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			OdcdJ+rfBT9iTBJsgDDz8ubXqlU=
			</data>
			<key>hash2</key>
			<data>
			v2I5YkdfNA3iW35JzKnWry9v6Rgvr0oezGVOuArREac=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/GbrImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			19q6ACaPrNc0DbT1RaENNRcS8Yk=
			</data>
			<key>hash2</key>
			<data>
			5t0UfLubTPQcuDDbafwC78OLR7IsD5hjpvhUZ5g8z4A=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/GdImageFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			4Wh7adBlqtinNFfwkabkTH2hS7g=
			</data>
			<key>hash2</key>
			<data>
			LP4Uxv3Y2ivGZIyOVuGJarDDVS7zK6F1Q6SNl4wyGuQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/GifImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			cKQ60J3/IEjRrvQK1FAJgdL0bRs=
			</data>
			<key>hash2</key>
			<data>
			on5rSz2AVIGfbfPO0cchIDDTXzOKUNfaGwFwaScymbQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/GimpGradientFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			p61eYlYc1uSiKAVlPFn50T7HWUg=
			</data>
			<key>hash2</key>
			<data>
			Z/4TUYMdPyUsiP40KSIpMJ5yLGMnBaIKOAkHyiQGEWE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/GimpPaletteFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			KdUZGEviU5vW1j/i5ebuQTPfS8s=
			</data>
			<key>hash2</key>
			<data>
			YHEhKThsEVlXVjFQUnGvhDgNsJcfFqUAN0O0ucG9G+Q=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/GribStubImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			OhliEz7G5E7XRvbNWXXhnMHwIWw=
			</data>
			<key>hash2</key>
			<data>
			degHg344X3JXL8u+x8NWn08BsmM9wRh+Jg08HHrvfOc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/Hdf5StubImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			eHaiZbqSjj2pHQ1tlIPClbl2YUU=
			</data>
			<key>hash2</key>
			<data>
			OuEQijGqVwTTSG4dB2vAyQzmN+NYT22tiuZHFH0Q0Sw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/IcnsImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			c2z2BeI5YtwSNIW+/HZ9nb8Y9VQ=
			</data>
			<key>hash2</key>
			<data>
			qvi+OP0g8CRlNlJE++5/rPlfyxLFLlSOil66Fw4TMwU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/IcoImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			ZrtHQdDSSmlamIJFz0WR0OUuVhc=
			</data>
			<key>hash2</key>
			<data>
			xrm70saxz/Ej10j5hEo+e80kNlgU9qtydbTKcmR8dIA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			HAmyL7m2Azvm/9Fu7PDdIfoDO0s=
			</data>
			<key>hash2</key>
			<data>
			wo5OL2PAcQW2MwRkJnS+N16toZzXWL95jx9FBM7l9ok=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/Image.py</key>
		<dict>
			<key>hash</key>
			<data>
			O49tq3eQTEIo4WOJtjxQxFluQqQ=
			</data>
			<key>hash2</key>
			<data>
			PUUvNgDjB8xz+/KyRwqg0CdWbcqXgqD6UeV8kR3dTP0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageChops.py</key>
		<dict>
			<key>hash</key>
			<data>
			VUtAxP8cwP7WoGKh6hqL09r9F1s=
			</data>
			<key>hash2</key>
			<data>
			GEjlymcoDtA5OOeIxQVIX96BD+s6AXhb7TmSLYn2tUg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageCms.py</key>
		<dict>
			<key>hash</key>
			<data>
			yHMNLBt0TecNgPso7wOe/KuXkoA=
			</data>
			<key>hash2</key>
			<data>
			wpVg1Kmp5WfeCNbEfGUCZsjcWVreg3HZqMHyTttlz1s=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageColor.py</key>
		<dict>
			<key>hash</key>
			<data>
			7XjxfBRR7QGt0+Pa/vUzw3PIi7A=
			</data>
			<key>hash2</key>
			<data>
			IGA9C2umeED/EzS2Cvj6KsU0VutC9RstWIYPe8uDsVk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageDraw.py</key>
		<dict>
			<key>hash</key>
			<data>
			Q7ecWt5epgb2FbN4/8Yj1epJB1I=
			</data>
			<key>hash2</key>
			<data>
			Of8nTRCoVtukMjJv4JzQszfm5qu0AIAj0a3a03txY+Q=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageDraw2.py</key>
		<dict>
			<key>hash</key>
			<data>
			/UauigPiUhG0yTX47gkxkX1zCOk=
			</data>
			<key>hash2</key>
			<data>
			pdVMW7bVw3KwhXvRZh28Md4y+2xFfuo5fHcDnaYqVK4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageEnhance.py</key>
		<dict>
			<key>hash</key>
			<data>
			8l9lqtuRXl5yn8etEtKVZXI7Tsg=
			</data>
			<key>hash2</key>
			<data>
			4Elhz/lyyxLmx0GkSHrwOAmNJ2TkqVQPHejzGihZUMI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			CQs2QCI3epv/75xd8GakKZ5jFDg=
			</data>
			<key>hash2</key>
			<data>
			9T2/oexZI/Oum0Df2Smv3wTiqF/lnh0JAOevaGrloW8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageFilter.py</key>
		<dict>
			<key>hash</key>
			<data>
			Qww1aVPV3CrosMeM3H5trxTgcZw=
			</data>
			<key>hash2</key>
			<data>
			MiTowY9micg1dSfwZkExXSBNPr2b/11kDCGreP6W8x4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageFont.py</key>
		<dict>
			<key>hash</key>
			<data>
			WA/fUUcLzw2Zbxtq+fQ9OasvXAs=
			</data>
			<key>hash2</key>
			<data>
			hJxlXI/qde0yXNOZssYBoRC1zWy6mgGv0WYuvHBUWgA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageGrab.py</key>
		<dict>
			<key>hash</key>
			<data>
			4+bBUzeO9qojVfei9jnr2K+ioVA=
			</data>
			<key>hash2</key>
			<data>
			NiOCaVKvksUkRRIwpipF4Y0/FeqbN2AVCh0tXaVV8Ds=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageMath.py</key>
		<dict>
			<key>hash</key>
			<data>
			yWjnbNWNEAy5tdKCel6LoOoXNEQ=
			</data>
			<key>hash2</key>
			<data>
			qDVyqP24n4FnCgJRgW/DVcRFoTdZFJLQd5qxAZS4EG4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageMode.py</key>
		<dict>
			<key>hash</key>
			<data>
			hIGWpXy1gm6PhIXPDVmrVo5XZJ8=
			</data>
			<key>hash2</key>
			<data>
			5yOxODAZ7jG03DsUFrt7eQayTtIpWPgvfyhlXDWwcv8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageMorph.py</key>
		<dict>
			<key>hash</key>
			<data>
			Ra6gHuCvVNkq5mPZaf6XSP01LzY=
			</data>
			<key>hash2</key>
			<data>
			TowXnk1Q2wX9AXVBDWRRQhCfAbFOUWGMo00vq4yn+fU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageOps.py</key>
		<dict>
			<key>hash</key>
			<data>
			S8xmfSIATF4rXXq7xs/E4alnLXk=
			</data>
			<key>hash2</key>
			<data>
			A69qjt+mxDI99387z/4cHI+wtH85SLL/ENTI9EeOQGI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImagePalette.py</key>
		<dict>
			<key>hash</key>
			<data>
			lhc3M973I+ZIMhWx9dpBurN6YYo=
			</data>
			<key>hash2</key>
			<data>
			M5tYUgadWR7mxUEByyVl7IV9QFFzAGiKKmAhCZtdG0w=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImagePath.py</key>
		<dict>
			<key>hash</key>
			<data>
			nbqX54XTJKzq5x63svEbrsp9aMc=
			</data>
			<key>hash2</key>
			<data>
			5yUG5XCUil1KKTTA/8PgGhcmg+mnue+GK0FwTBlhjw4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageQt.py</key>
		<dict>
			<key>hash</key>
			<data>
			rFVQqufGJLGHX7sMat3bVEVZR80=
			</data>
			<key>hash2</key>
			<data>
			dQbadF2Lg59OJVjiNVcbz3wvymqEpL+uEZG32b8E+bg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageSequence.py</key>
		<dict>
			<key>hash</key>
			<data>
			GkRBTI2xfOUmq9YAKdIVdAv+Tnw=
			</data>
			<key>hash2</key>
			<data>
			gx2EvywPBEjxNJujCqdpbfAm2BpyNV2/f1IaO3niubw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageShow.py</key>
		<dict>
			<key>hash</key>
			<data>
			byiEdNtHvzwG62cLaIjAPohoqVc=
			</data>
			<key>hash2</key>
			<data>
			5Gaj1dS+eMHoA3ajRIqt3DpAvgvOBCKaP8kepkeNYhA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageStat.py</key>
		<dict>
			<key>hash</key>
			<data>
			K5rcNhvB+6IW4fRlA1a5XnZKZds=
			</data>
			<key>hash2</key>
			<data>
			S43FZ89r/u4hKCj59lVuWpyVJfhbUy3igXkp9DwaMgM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageTk.py</key>
		<dict>
			<key>hash</key>
			<data>
			1dGQ1gsW30rgxmPOdNMg74ovNcY=
			</data>
			<key>hash2</key>
			<data>
			b5SntckGXs0ECsI2MmdJg3CSX6AtELsWh0Ohxu41u/k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageTransform.py</key>
		<dict>
			<key>hash</key>
			<data>
			+5ZxzhS4DOmWAzDs9By/GZKZC+g=
			</data>
			<key>hash2</key>
			<data>
			+qek7P3lzLddcXt9cWt5w/L11JGp2yY3AJtOfmJAkDc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImageWin.py</key>
		<dict>
			<key>hash</key>
			<data>
			g0Ie7ppsrN894nl3I3foqsw2MN8=
			</data>
			<key>hash2</key>
			<data>
			LT05w8/vTfRrC3n9S9pM0TNbXrzZLEJHlCJil7Xv80k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/ImtImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			ygXXr2oMWM0s3Ny9yxTh2hhliog=
			</data>
			<key>hash2</key>
			<data>
			SL5IrsHcblltxtX4v/HVFhYnR6haJ0AOd2NHhZKMImY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/IptcImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			ZlXAvFIeBxJJ6jG6bvQf1xYuwAg=
			</data>
			<key>hash2</key>
			<data>
			zMOEYveSc8ph1WdJtC+tUJEueDcInpVUviCcnqKXq0Q=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/Jpeg2KImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			vUSHt+qpGlc39QClT+Iq5txyBl0=
			</data>
			<key>hash2</key>
			<data>
			k9UoU7+Hq8vAWi9ZoosA4bfufNJsctBd4ttM1RFxwnk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/JpegImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			CH7WoEGLyzLfhekADxW3NSDNyPY=
			</data>
			<key>hash2</key>
			<data>
			0m+NrfJ3HaDStPj/crfRjsZYLYQxnW0Pj+VU8XlebmI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/JpegPresets.py</key>
		<dict>
			<key>hash</key>
			<data>
			9TDwQ5UBcqe8X9gstYq31Yw7Q2g=
			</data>
			<key>hash2</key>
			<data>
			lnqWHo4DLIHIulcdHp0NJ7CWexHt8T3w51kIKlLfkIA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/McIdasImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			3UxCaT4oeN2fgisoLTGgDrd9kLE=
			</data>
			<key>hash2</key>
			<data>
			IC+tgSxhtCyI9RkUOvy0puvlHYT9u/zZfn5TVnE7aeU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/MicImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			aF9iq77UR/4A/1va7ZOu8p+hlIY=
			</data>
			<key>hash2</key>
			<data>
			aoIwkWVyr/X+dPvB6ldZOJF3a9kd/OeuEW3say5Y0QM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/MpegImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			fF1hQL0N+EuijvoTWHQJWOCjKf4=
			</data>
			<key>hash2</key>
			<data>
			jCgGXdyYGMHvJ36RB+/8DMcSTxq4Wqg5NRB01w4o/Rg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/MpoImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			QnHSSymhuCaIt8pCV7UTyIJxynU=
			</data>
			<key>hash2</key>
			<data>
			IaoOsLN+IZ79dmsTYCbwu5GYBQOY6HfrgOrGgEH9jwk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/MspImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			5lATBZT2sfPfQeyH8mRBpBWIV1w=
			</data>
			<key>hash2</key>
			<data>
			oxk/MLUDvzJ4JDuOZCHkmqOPXniG42PHOyNGwe60slY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PSDraw.py</key>
		<dict>
			<key>hash</key>
			<data>
			1Z/J+bJsj6F7Iy9/CKk78OXXGUM=
			</data>
			<key>hash2</key>
			<data>
			KMBGj3vXaFpblaIcA9KjFFTpdal41AQggY+UgzqoMkQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PaletteFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			5+85PiLpJVSz6ysUGvjwwAobOrY=
			</data>
			<key>hash2</key>
			<data>
			suDdAL6VMljXw4oEn1vhTt4DQ4vbpIHGd3A4oxOgE6s=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PalmImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			bHCLS9NVvaZLa44WbDCiWvy3Ux0=
			</data>
			<key>hash2</key>
			<data>
			WJ1b8I1xTSAXYDJhIpkVFCLu2LlpbiBD5d1Hr+m2l08=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PcdImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			rvP1lggpQpdT0yYidPBx/xVtXdU=
			</data>
			<key>hash2</key>
			<data>
			VweZ108HBHeNEfsoE26EOR4ktxqNGSOWOnd58DhS8Fo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PcfFontFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			QJWlI4Iq/SGZz/UxK75zROcLjQU=
			</data>
			<key>hash2</key>
			<data>
			NPZQ0XkbGB8uTlGqgmIPGkwuLMYBdykDeVuvFgIC7JU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PcxImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			ksNtGrwlM6jJMO/4gFtJGhIdFhc=
			</data>
			<key>hash2</key>
			<data>
			HjI2NZIf1nnkBkB0NOLMEBiWu+B3h5Uid1qI9qnLb1c=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PdfImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			UA08B636kLCkrbSNH4pPRLY4scU=
			</data>
			<key>hash2</key>
			<data>
			AbJA2f4qzH8G1olfmk18SzQlcx3WsipUYDc5bcR8Wvk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PdfParser.py</key>
		<dict>
			<key>hash</key>
			<data>
			fpETOhsScDq7SAd1+jzdS55qGoE=
			</data>
			<key>hash2</key>
			<data>
			LnmX0Cm7ZQwGkB1uYP4rvXZUkERmURzmYo78zjeq6VI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PixarImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			xr/MNbeoTa+LOF31RUGo0D2Yfmg=
			</data>
			<key>hash2</key>
			<data>
			l/4GwBd0mATnIXYJbwmmODU2vP7wewLu6BRviHCB2EI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PngImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			C8PitpierwAMaRptKniG6qNDeNM=
			</data>
			<key>hash2</key>
			<data>
			G/qXWPponC8vpAtBIoPEMeenWdsGbKb3S16/UD75gZg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PpmImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			y0+1684bgb5yiu7I882qjHLmtuw=
			</data>
			<key>hash2</key>
			<data>
			YRKWH1raOHFM+cYXCz5w1+842ICursnR9E4lgHaXrw8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/PsdImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			H/7j0+2fjIwhR4ey8oNEMqx7D1s=
			</data>
			<key>hash2</key>
			<data>
			ImnNRG4VANs2GATXVEB5Q+yy1Jskc6XRVRtZYi2fALg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/QoiImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			HZ1nNBo4NqeYjFl6TAUTLoxKW/A=
			</data>
			<key>hash2</key>
			<data>
			anFLgwZVkVOZaeLQeucXESW2qIdKRhAFvcdfALx5/kA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/SgiImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			hlZzxoAiSn3z7K4zTce1rxqajuk=
			</data>
			<key>hash2</key>
			<data>
			wjO3mgTO7AYC2Bs6RJBEKafm49wgFkCXZuVoBD6UWxc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/SpiderImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			D83Oiwt3h1X0b2FM+lrSPlvDZuI=
			</data>
			<key>hash2</key>
			<data>
			Bsg6pfZMctas1xYx//oL+ZZseUReZdnLy5a+aKEJhpE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/SunImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			T56qhS0i6Bo4m2IjFQ+xbKDDQXU=
			</data>
			<key>hash2</key>
			<data>
			Hdxkhk0pxpBGxYhPJfCDLwsYcO1KjxjtplNMFYibIvk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/TarIO.py</key>
		<dict>
			<key>hash</key>
			<data>
			Clv5sk8SyI/6wrlNfzXFBwTXcwk=
			</data>
			<key>hash2</key>
			<data>
			BqYUChCBb9F7Sh+uZ86iz1Dtoy2D0obNwGm65z1rdc0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/TgaImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			AIuwCE6UtjS/glXSeQibyUUh3ak=
			</data>
			<key>hash2</key>
			<data>
			2vDsFTcBUBHw1V80wpVv4tgpLDbPr6yVHi6Fvaqf0HY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/TiffImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			ThV1y32wa4G+7CJWe7/tN4sv8Os=
			</data>
			<key>hash2</key>
			<data>
			bxxUUgBX8qTmDPrIbz1Hpcp4sjls0h4Wetey12z9Tx4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/TiffTags.py</key>
		<dict>
			<key>hash</key>
			<data>
			2Z/yFrVgUGpEYH4iWdAuoC4UYeY=
			</data>
			<key>hash2</key>
			<data>
			+gbXLZ5rlHD6crwtY6TkafDm2tamlc5v8e7FjS8PcIg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/WalImageFile.py</key>
		<dict>
			<key>hash</key>
			<data>
			8KgHbI3o4rAO2X+ESDQRiUDmMG4=
			</data>
			<key>hash2</key>
			<data>
			Lfuq/WZ/V/onwucfUc6GWfvY7z/K4s+5EdaQGu/2DD4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/WebPImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			03HPkR5DVAc4bsjHtN2TfMSE0wg=
			</data>
			<key>hash2</key>
			<data>
			YFWo6/FYBSrzAf6XMbmrF4YRtR4x7tYecCWF7EA13WQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/WmfImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			K3csG3r9UlCgdDdPZBzbYNu2uao=
			</data>
			<key>hash2</key>
			<data>
			bTwZLMX5iPdXtLjCRpM9PcosxUyYO64UlQLg8nx5YWs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/XVThumbImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			fxXkY9c4J1DxZ74DokK7rwbAUfY=
			</data>
			<key>hash2</key>
			<data>
			cJSapkBasFt11O6XYXxqcyA+njxA5BD3wHhNj6VC7Fk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/XbmImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			bb7d7BPT/eNEZKv8uMlY2qS6w/E=
			</data>
			<key>hash2</key>
			<data>
			Fd6GVDEo73nyFICA3Z3w4LjkwoZWvhHB6rKCm5yVrho=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/XpmImagePlugin.py</key>
		<dict>
			<key>hash</key>
			<data>
			ce55WsnA26fYvEYdboN5z6fFu1I=
			</data>
			<key>hash2</key>
			<data>
			k/SecMeAPVuXIZD/B0X6mTAyUyWF+iso05GZYh4ogyU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			49BjUkryCH/NU9XCIJGT0QQQtJw=
			</data>
			<key>hash2</key>
			<data>
			Q4KOEpR7S/Xsj30fvOsvR94xEpX4KUsVeUwaVP1fU80=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/__main__.py</key>
		<dict>
			<key>hash</key>
			<data>
			RZ7iCPhrB+IkxjRWIxA66GGMHjQ=
			</data>
			<key>hash2</key>
			<data>
			Lpj4vef8mI7jA1sRCUAoVYaeePD/Uc898xF5c7XLx1A=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_avif.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			/mDvtAru7imYuwckXU+Vca0Igl8=
			</data>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_binary.py</key>
		<dict>
			<key>hash</key>
			<data>
			Xh10GE1pTHiTvMggE1skUDXC/5E=
			</data>
			<key>hash2</key>
			<data>
			pcM6AL04GxgmGeLfcH1V1BZHENwIrQH0uxhJ7r0HIL0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_deprecate.py</key>
		<dict>
			<key>hash</key>
			<data>
			Es5FuOwnf5y9Gy9Ktis9GQbc8xc=
			</data>
			<key>hash2</key>
			<data>
			8r0NRI3xw/cjpmxZbhUkfWVS82M2vD5uyowH3X/MMD4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imaging.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			GAdmwaP8uxma1hfzFrI8FeJLOwY=
			</data>
			<key>hash2</key>
			<data>
			D3Flp/wzW2o5+Zrwi1Yz9w+sBlCQPW3hAQmE2dU9b1A=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imaging.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			ytlEs9K8ZTJhjhzZ4YYdqhY5euo=
			</data>
			<key>hash2</key>
			<data>
			StMbXUZS32AegATP1sUHfs5P05A3TD/BiQKsDHQBW40=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingcms.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			ou18KOSbXZT88maloHxrGOBP34E=
			</data>
			<key>hash2</key>
			<data>
			2s3LMaBrHKEVv1hcrsJbHZI7j96r2701uI8lIjj9S4g=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingcms.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			lpLLyFGYfeDqJoKwvb+AAZ2THz0=
			</data>
			<key>hash2</key>
			<data>
			brpjxRoiY/2ItyfTrjhKeGEsExe4GPG+25q9AQP8Jp8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingft.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			ZT162zRRooaT5IZZjz9RISftyDo=
			</data>
			<key>hash2</key>
			<data>
			Qwwrq9Rhd4WU0tFGP1CCrt+oPTpib7vkJZVUCNTlHyc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingft.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			BxJj11RWU/Tc4rsrOaNmIDN7B3I=
			</data>
			<key>hash2</key>
			<data>
			IYdFGfApwsqYiJVoD5AVOvgMvnO1eP1J3cMA6L0YZJ0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingmath.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			3HStkuXJjjri21dPjJ/NTk6H98M=
			</data>
			<key>hash2</key>
			<data>
			oDBMExFxGBiJiyqGLIGSHsxq31ygkjydVCEvZnxCbg4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingmath.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			/mDvtAru7imYuwckXU+Vca0Igl8=
			</data>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingmorph.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			OvxLTyQ4zdExvCVEiOu8cSZ1U7U=
			</data>
			<key>hash2</key>
			<data>
			tZ0OFp6ZWglPSDtHHotCw1DAQAKChQIrmUJTZDe+wQY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingmorph.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			/mDvtAru7imYuwckXU+Vca0Igl8=
			</data>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingtk.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			TwB3D1ml9JmWSqAnK53rXMN6g3U=
			</data>
			<key>hash2</key>
			<data>
			xh/uESa9tAv+2XxR/CVKO582LuzTey8HMkjnQNcqSlw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_imagingtk.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			/mDvtAru7imYuwckXU+Vca0Igl8=
			</data>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_tkinter_finder.py</key>
		<dict>
			<key>hash</key>
			<data>
			yyxdZNdrQ54urIiGJfYXFc0CCTw=
			</data>
			<key>hash2</key>
			<data>
			GIZ4stmFhUosmHKSrdxcjStiocDNfyJn7RBie2SWxU0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_typing.py</key>
		<dict>
			<key>hash</key>
			<data>
			hGsBgxXV+bMI17wDOcy+CDQM8WE=
			</data>
			<key>hash2</key>
			<data>
			1NAWJ7Z59TP98cFv9qGpBMgSHbyR4CAByLjMRRbSZxY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_util.py</key>
		<dict>
			<key>hash</key>
			<data>
			dOiC/hXrHDbnsoOw+J+o0WGg2qw=
			</data>
			<key>hash2</key>
			<data>
			E76J1WLAe6Xg5yNWYztQwYzxUT/sR/VQxFJu7IZ3S3k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_version.py</key>
		<dict>
			<key>hash</key>
			<data>
			iPCjzMfv1ajMgTU0s+P82ncs1Fk=
			</data>
			<key>hash2</key>
			<data>
			bxnTaPfB+9RcqSsk1Cm99GpVMVKFtvumfDQsrVbwBIs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_webp.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			ny2f/t+X0xQduI0NEZNxqUyzEts=
			</data>
			<key>hash2</key>
			<data>
			hTcOHb1FMjP0VPCa6XPAMiKMneYC3LuG/dRuJ2Eajkk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/_webp.pyi</key>
		<dict>
			<key>hash</key>
			<data>
			/mDvtAru7imYuwckXU+Vca0Igl8=
			</data>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/features.py</key>
		<dict>
			<key>hash</key>
			<data>
			zAV/O3uGO3ro6BJBRClzSn8FWW8=
			</data>
			<key>hash2</key>
			<data>
			FfyYObVJbzYQUXf8KuRuqY6kvA8md2LorE81k3EuQrw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/py.typed</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/PIL/report.py</key>
		<dict>
			<key>hash</key>
			<data>
			W3HeUe8v91YeUhwXPRXf7DZvUG4=
			</data>
			<key>hash2</key>
			<data>
			4JY6+IU7sH1RKuRbOvy1fUt0dAoi79FX4tYJN3p1DT0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/csv.py</key>
		<dict>
			<key>hash</key>
			<data>
			rLLmKHVXh96QxVYOXFMb8dRSX5U=
			</data>
			<key>hash2</key>
			<data>
			cify1HdPuITVa8wRt95TZo74ZA75xR7azryozTXXofc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			Wln+Qw0tEjfEuvROIKJm7gguSEs=
			</data>
			<key>hash2</key>
			<data>
			/eLjpybhQB49OORUMIU+op8w0/W4Gxus5AQi3yzQcjg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<dict>
			<key>hash</key>
			<data>
			INBhs7dCz6MeX7yGLTT1V1NO/b8=
			</data>
			<key>hash2</key>
			<data>
			+tZ+KwYMMYtshkbQh/vTrdk4tmdiQ/FLDFJiMXlkEnQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			/dyLHGiO87rtDVpGq/XwHw7a8Cs=
			</data>
			<key>hash2</key>
			<data>
			SsjgNgb/pMN/YaZRCiCA8fN6cFT0cmwhSIfTsj9y42k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			hNECSIc4sOu8elCHlz7/vVTJW9U=
			</data>
			<key>hash2</key>
			<data>
			MZz/bnox8PKkHEddykKJCqXRn+FgF+IpD4wdThT3ZIE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<dict>
			<key>hash</key>
			<data>
			s2ExZOWH0JwFLDTM3E1E2sT/ROI=
			</data>
			<key>hash2</key>
			<data>
			EjTAF8hx6y4g029mj5PgZs3Lk9tGTVzvnXpb+DUG0ow=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/assets/themes/blue.json</key>
		<dict>
			<key>hash</key>
			<data>
			Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
			</data>
			<key>hash2</key>
			<data>
			yUF0cMFs7XpD1sSo4Cevpu3GLCTVrufEwtzRE4WWTTs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/assets/themes/dark-blue.json</key>
		<dict>
			<key>hash</key>
			<data>
			+evgfnnhRvedyIp/+JQsDkMEnw0=
			</data>
			<key>hash2</key>
			<data>
			alf6b4+4lhowzmQpUisYDXbjr5uODarCWQWYQThqa9M=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/assets/themes/green.json</key>
		<dict>
			<key>hash</key>
			<data>
			TwB32rbJhqZKuTkmMAJMsJdyseg=
			</data>
			<key>hash2</key>
			<data>
			ea2Gv+p/BVesHiCAKJKrtEqWevFbkxWwA5zXXItyp3Y=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			AXewfnqN2cPZWrlNUOH0rZhgE7A=
			</data>
			<key>hash2</key>
			<data>
			eSoo0x/4XkSok/BaQbtM+AtwMRu1za6MPhd6YfWz6sI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/ctk_input_dialog.py</key>
		<dict>
			<key>hash</key>
			<data>
			qSCFzm7g9oLaNqpIJMcLjHf7Bc4=
			</data>
			<key>hash2</key>
			<data>
			/mAWVEBSPiuBgISbXaWE8LG5Fs7hp0KOM+BmVfBwnp0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/ctk_tk.py</key>
		<dict>
			<key>hash</key>
			<data>
			RZn0egqCBDOEiXhq9HMpF/KRn0c=
			</data>
			<key>hash2</key>
			<data>
			pvYD7mP9SXdVKIomRbNKt0Zu2pFsIOf3246DqPFz1Pg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/ctk_toplevel.py</key>
		<dict>
			<key>hash</key>
			<data>
			zdwCZ0vykVwxFvchH7u7SJXsyLU=
			</data>
			<key>hash2</key>
			<data>
			jEaJpXnAXW6wsdeh3Tr9oABk39GSuk0Z/7OgG8F9Nf8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			NLtGXvMXOGjzl+HX9s84CV7gFwA=
			</data>
			<key>hash2</key>
			<data>
			DNohOpF5R2Ae1iP4wNpsDYkOnMRiVrcNdwCPWIxNWGI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/appearance_mode/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			WualDnHMf0Wdr1nGiXFo3sf1Q0Y=
			</data>
			<key>hash2</key>
			<data>
			fKKK0fVUHFiqmSZw0GZFY0GKgJGDs8A5OXvSBEC3qSg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/appearance_mode/appearance_mode_base_class.py</key>
		<dict>
			<key>hash</key>
			<data>
			67qSTVWF6ipthpMxiQ/JOUIr2rw=
			</data>
			<key>hash2</key>
			<data>
			nTXJC4vpwjrgCiaOv3V20xVvtkxKR9pVJykIkhWKFz8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/appearance_mode/appearance_mode_tracker.py</key>
		<dict>
			<key>hash</key>
			<data>
			r5uR2pG0IVovgE5AU9i3b8dfAGo=
			</data>
			<key>hash2</key>
			<data>
			L+X+eLxWFhenWtHr7Z/Mp5msaBTMEIxc+hhKrIVn7bc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_rendering/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			+o2+YODiaiuggPPrb5Cl0vGp/Oo=
			</data>
			<key>hash2</key>
			<data>
			212WNYQ90/4YY/QB6x0E+xCqqmUXsWegNPQ4VeXSKO0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_rendering/ctk_canvas.py</key>
		<dict>
			<key>hash</key>
			<data>
			xn7Sx4zGdnTxI4GHtdXVETtVm78=
			</data>
			<key>hash2</key>
			<data>
			uXHYqdTns1LAp2GYadyBDcKt5PpNUHka/7l4aV1jqqg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_rendering/draw_engine.py</key>
		<dict>
			<key>hash</key>
			<data>
			ojZuYVXjCkIgcLsohWog5oM1CN0=
			</data>
			<key>hash2</key>
			<data>
			eeXpsYiCDOqUQo5Pz5nBHDQqg99drt4QDjODmrdee8w=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_widget_classes/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			at5fLm39WwpKIVSGQ4CatYcbGpk=
			</data>
			<key>hash2</key>
			<data>
			DsG3zo4hzAUmdFbt3aXgen0Vs0Y9+ppycKTIfrHTYMA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_widget_classes/ctk_base_class.py</key>
		<dict>
			<key>hash</key>
			<data>
			3r+t6ku1t4rv2CYmhJYzek9192M=
			</data>
			<key>hash2</key>
			<data>
			PTxPeqvfnct+wuXBqodbGDxpfXJ67Cal201qJxCt3EE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/core_widget_classes/dropdown_menu.py</key>
		<dict>
			<key>hash</key>
			<data>
			YLCnwzZUoLDGhiIVmY1aNF9J+Mk=
			</data>
			<key>hash2</key>
			<data>
			dC/SEWk/XQbX47NjLDyhsKh8YKNyPFndoIIxqAnOGRE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_button.py</key>
		<dict>
			<key>hash</key>
			<data>
			hL7fPuTeOE8JX/nCmF1agDYdSQU=
			</data>
			<key>hash2</key>
			<data>
			TE/6T88cL8ae1p8ZOy/WE4inpxjP0iaXej0qmjcjbCU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_checkbox.py</key>
		<dict>
			<key>hash</key>
			<data>
			SKY9XY8AYnVfnY6Xt742sBbkCzY=
			</data>
			<key>hash2</key>
			<data>
			h8w7SG/9oWE4x4/iBQ+qXvxTq1J/o0Dj4C6JZAqXANU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_combobox.py</key>
		<dict>
			<key>hash</key>
			<data>
			0R1YvJ9PfAXy9bqqyHxeyFHdbjA=
			</data>
			<key>hash2</key>
			<data>
			p1dYS2r9nq7BFfidIbt9JCaZRB8XtjV4XHUV8nGpdGo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_entry.py</key>
		<dict>
			<key>hash</key>
			<data>
			1elVPws9ZuUIXDghjd/lE1xP1fo=
			</data>
			<key>hash2</key>
			<data>
			bxhcZAQt81S59UA5v9fJtsXLLHf9NuUy4qDh4K2m+Ec=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_frame.py</key>
		<dict>
			<key>hash</key>
			<data>
			68Qx4SXoqMrhE6u5DHT2mEjfeF0=
			</data>
			<key>hash2</key>
			<data>
			hcyGh8OzB2CzwCb9c4DMC29QqJS9awzNzEZZmzv5Usc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_label.py</key>
		<dict>
			<key>hash</key>
			<data>
			WkiWnC7OVjXd2H9D6f+kbZlCzbA=
			</data>
			<key>hash2</key>
			<data>
			4WDRL/gbctV9t+SIKp1oowbvXB+x3CN5o5yajrOogxE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_optionmenu.py</key>
		<dict>
			<key>hash</key>
			<data>
			kKikSLPv5f9kH3a1w9yIkMOWKp8=
			</data>
			<key>hash2</key>
			<data>
			5g2OzI6mexzGZxmrUStsyuG+MiihAr+UhyzVS5y5vko=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_progressbar.py</key>
		<dict>
			<key>hash</key>
			<data>
			GtCBlk0WUEmYJJqqRDl867NMz+o=
			</data>
			<key>hash2</key>
			<data>
			k8aa/RUIVb+gNUsOUxIHAzBkw8yDgZuU0ar5JeRp4EA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_radiobutton.py</key>
		<dict>
			<key>hash</key>
			<data>
			F3vvOznnrSdIQi5lxNwxZCPVhH0=
			</data>
			<key>hash2</key>
			<data>
			hEY0uDuStmkizlpkP8QsfLXRgRNexaj/JPM8/2Iyf3Q=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_scrollable_frame.py</key>
		<dict>
			<key>hash</key>
			<data>
			lX0RUgyc7OU3jaB4YTq2ls/7FxM=
			</data>
			<key>hash2</key>
			<data>
			wKJI1U36m5FhDjIcuDoYPWbTQbLDtusBXu3c1UYQOzk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_scrollbar.py</key>
		<dict>
			<key>hash</key>
			<data>
			f8NisTav3ahbJ6IaLZx2edgJNxQ=
			</data>
			<key>hash2</key>
			<data>
			cO6NwExutdSXUBSRdbVve9b0dvV9djUs9Pxetqfxfn8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_segmented_button.py</key>
		<dict>
			<key>hash</key>
			<data>
			DZBt5jqwZ/sIa6gMqdUBOsYgvtE=
			</data>
			<key>hash2</key>
			<data>
			zKPj1J/ZbEXbt5en7vHdZhooqNIrDkr3DKo+QEQ3rvc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_slider.py</key>
		<dict>
			<key>hash</key>
			<data>
			SWtK8a05rMVUshb8gcf1s9/GXYM=
			</data>
			<key>hash2</key>
			<data>
			1xY9vnha+djmym0iLpBkVK30aCDw92lqTMOgZiJ4Hhk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_switch.py</key>
		<dict>
			<key>hash</key>
			<data>
			oFmeJTKdwVw9lURm7K9zsen3pxA=
			</data>
			<key>hash2</key>
			<data>
			aOscR68hoHVkmugnWMqTlsKlLZgGCiO1rfXkDOQSC0Y=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_tabview.py</key>
		<dict>
			<key>hash</key>
			<data>
			YAYwjG6LJQw3wOxSpH+XMHVOcG8=
			</data>
			<key>hash2</key>
			<data>
			ieGuEQqUH7miPQWIkSyon7KVWdbqFFKW5k7oBv53jRg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/ctk_textbox.py</key>
		<dict>
			<key>hash</key>
			<data>
			vVDQCAh9Pnjq6qOmHVu4qxCm/co=
			</data>
			<key>hash2</key>
			<data>
			l+XEQQYVKMB7cTEk4h150b2s+o2HpRh7zHROVc9H3bA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/font/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			M7kEVJFHYh4wCdun8tX/SWPZOa8=
			</data>
			<key>hash2</key>
			<data>
			zYlSsJH+8rL77t7dkz0vXOofHh8NdiIuMQK4V8mfINM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/font/ctk_font.py</key>
		<dict>
			<key>hash</key>
			<data>
			f83WvnTzAklSbVSvPwsGykC3YI8=
			</data>
			<key>hash2</key>
			<data>
			5cdHvujB/tfDlsw9ZRMiGBASvmRSg/WIbSF99x/21vA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/font/font_manager.py</key>
		<dict>
			<key>hash</key>
			<data>
			HiGTReaV/YEWFcX9fzdDi/xRkcs=
			</data>
			<key>hash2</key>
			<data>
			jwpu61Z00noq0UWcFaHftAMRvXgmKQHEmQiDq+CrqNw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/image/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			m780kLv9afgV0QQwheBjmtOFykc=
			</data>
			<key>hash2</key>
			<data>
			pzbwlIAV4OE+CkdxJIrl1ELcRg7vbQKkC6HaHLGDNI8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/image/ctk_image.py</key>
		<dict>
			<key>hash</key>
			<data>
			yiHJo2Rgm7NKk95zLYLlobYxYvs=
			</data>
			<key>hash2</key>
			<data>
			uL/oJrlq8Z134qjiii44Lg03utETSf7rBJ6kgPHeYzk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/scaling/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			npm5iUcMgYK2XzKFeCOrekteQQw=
			</data>
			<key>hash2</key>
			<data>
			2GjeG2/A4gq8g7pROKPw6QR2mIH7b6iRDQvT26qz8R4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/scaling/scaling_base_class.py</key>
		<dict>
			<key>hash</key>
			<data>
			gLpwMzYjBP3CLztbHWJPDpwPxiY=
			</data>
			<key>hash2</key>
			<data>
			VbzzA9GPujOYbEJjDsQRnvoY6v7RSitZwr37lZAkk/8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/scaling/scaling_tracker.py</key>
		<dict>
			<key>hash</key>
			<data>
			sQlAqv7+IErafejTT0cUcCiLARw=
			</data>
			<key>hash2</key>
			<data>
			cuilXEeSTOylgDRNf2kh+eDQShup+i2Nc7oOcLo3Vtw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/theme/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			CiCr0sQnTlxvw8pAZlgPdJQ+uac=
			</data>
			<key>hash2</key>
			<data>
			iZ6T6O+v39hOLfTLuNbTLt0XA/raCYtxwGnmqNB2Lmk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/theme/theme_manager.py</key>
		<dict>
			<key>hash</key>
			<data>
			UVpdgFN/gIj6uifRhfT4a/h5bmo=
			</data>
			<key>hash2</key>
			<data>
			E+VtzWKZ2IXrc/hvfGUdj50GNr8eII7S/fWBk58xix0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/utility/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			5Yh9ARtnY0nRW+ttuAuli1j0jzc=
			</data>
			<key>hash2</key>
			<data>
			STsgOQvThAoUarV9ovM6M40araQUDNU/QmmHbyMaRx0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/customtkinter/windows/widgets/utility/utility_functions.py</key>
		<dict>
			<key>hash</key>
			<data>
			Ud9BJrZWgGxK8WjMwEizs6e4fq0=
			</data>
			<key>hash2</key>
			<data>
			d0Wj5Aioxhb3qtC0ITuNZHjwfFHegUfK+bfkDXrrufk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/darkdetect/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			cywQFimryK59A+UTsbaz4KrasHE=
			</data>
			<key>hash2</key>
			<data>
			q2BLQUpYqbfFzsXHVoQL38IKzwXH50aEFAMy4rBuh50=
			</data>
		</dict>
		<key>Resources/lib/python3.9/darkdetect/__main__.py</key>
		<dict>
			<key>hash</key>
			<data>
			fAifAKZPgik+PPUPIl/ERF8drQw=
			</data>
			<key>hash2</key>
			<data>
			H1iyyQbJFbpOtKpSEmmV9H5ZjcrAI3zPWcCv1min5Ww=
			</data>
		</dict>
		<key>Resources/lib/python3.9/darkdetect/_dummy.py</key>
		<dict>
			<key>hash</key>
			<data>
			CQtwWaIAgAcq/r0lwmPu+O45n08=
			</data>
			<key>hash2</key>
			<data>
			vKv/XK2dpvIGXZAjlNRuXiCH+vrRy7DBVCQKpbflrsM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/darkdetect/_linux_detect.py</key>
		<dict>
			<key>hash</key>
			<data>
			ClDMA4KQV46jtE9wtdESfE5nn/A=
			</data>
			<key>hash2</key>
			<data>
			nfyc7rpVNqMfbfGoXLsB7bPPSMG2cP1AauON73dCiKE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/darkdetect/_mac_detect.py</key>
		<dict>
			<key>hash</key>
			<data>
			RrallIiyPrDOdxYvalygPi0vH+4=
			</data>
			<key>hash2</key>
			<data>
			XQfJ9gcbEFWoZxFrrnrsh+AVwIWHV+/oMpk3UCVBMWA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/darkdetect/_windows_detect.py</key>
		<dict>
			<key>hash</key>
			<data>
			1fbI+bMAJqmvAfcKtHo6RShauiE=
			</data>
			<key>hash2</key>
			<data>
			8CCrPeTqNno1zVDXU5CP8D0AzOfqtcYOViHFnK3/WfA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/getpass.py</key>
		<dict>
			<key>hash</key>
			<data>
			HT/6zSSI9jHTzmaevCrWtQ/t1Kc=
			</data>
			<key>hash2</key>
			<data>
			50/URTN/9QMiPdiqS919BJFwZ9AMeWoQvtt6E4Gklgo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			m2YmbBObTEb7bnRgutC1H//9+1E=
			</data>
			<key>hash2</key>
			<data>
			4bk66hxOsw5JRhyy4I9U8c/VXK+pLusB+YB+aS86ot0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/__main__.py</key>
		<dict>
			<key>hash</key>
			<data>
			9NP9XwwwRygM77FnyAWYhF/pkP8=
			</data>
			<key>hash2</key>
			<data>
			vB/vOSk4pIZrkevBQeHXy6GYv7Nd0/vieKe44Xf1i9g=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backend.py</key>
		<dict>
			<key>hash</key>
			<data>
			1OdKXaD+2DrYYaKhVOJIrHx36z8=
			</data>
			<key>hash2</key>
			<data>
			hg5qqlLy2K/KSh2sZ6BM/nFbgIKjFhjz5iJwwsdqIHs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backend_complete.bash</key>
		<dict>
			<key>hash</key>
			<data>
			H2R/hP2m4SyEATDLn0cLp1zmhxU=
			</data>
			<key>hash2</key>
			<data>
			I3bRA3fGR/duzLrJyki94CaxxnelhiiXYyXLvUmlbec=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backend_complete.zsh</key>
		<dict>
			<key>hash</key>
			<data>
			SQu0mKZy+Wy3jHY6HYdNC0l3ZF8=
			</data>
			<key>hash2</key>
			<data>
			Je9QAn0CbF8/8ssGSkroa4HMcJDB3g20yL8XhhW50fI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/SecretService.py</key>
		<dict>
			<key>hash</key>
			<data>
			UxF4j6ANXzRUFpst16d9+58WKu8=
			</data>
			<key>hash2</key>
			<data>
			qt9lQpa8h6rGnjzTOE8GMIDH2e2J40RIhV3yc1TXSsc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/Windows.py</key>
		<dict>
			<key>hash</key>
			<data>
			iEQmh9mg6kKje52lXQl/C/E9qEo=
			</data>
			<key>hash2</key>
			<data>
			2pi3LSV2RCwXrLYeNplIUVJgPLH5uMnyYcSBgo+6kmw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/chainer.py</key>
		<dict>
			<key>hash</key>
			<data>
			2V9UlLVqIgSLLozkZsrF5X3bGng=
			</data>
			<key>hash2</key>
			<data>
			+hhe+UWbCn0PAUK+00cWjHz/JJNQf/N4OyHUn89yCOw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/fail.py</key>
		<dict>
			<key>hash</key>
			<data>
			26BRc9TTlu/a5TaaU7VTupgt8A4=
			</data>
			<key>hash2</key>
			<data>
			ef5uP3Ddj2apq2pe08LXI2lLgpkmN0UrKZmOx58UHIU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/kwallet.py</key>
		<dict>
			<key>hash</key>
			<data>
			8yEZudKMJL1zIAoIFe7Yz2Dy7JY=
			</data>
			<key>hash2</key>
			<data>
			tgOuwWl3+nJ2lEZPl15ORdoJRNLfcdVoz68xMWFUMlA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/libsecret.py</key>
		<dict>
			<key>hash</key>
			<data>
			y9tEWbBgCDszyG9Ur8xijLXQxqk=
			</data>
			<key>hash2</key>
			<data>
			gWeUveE44wZH0j7t2w2L+leYMpJOEHV0OqSUiC+sHQE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/macOS/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			Fvm5xOlcMvEi8M9bwSLOR0o2fwA=
			</data>
			<key>hash2</key>
			<data>
			+CIONvwrJFbeuj60opbCMZw4wWtiGyHuGCshocd4Ndg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/macOS/api.py</key>
		<dict>
			<key>hash</key>
			<data>
			nHbJRYrmTez7/O8Vlj0te0TDyDI=
			</data>
			<key>hash2</key>
			<data>
			eikiBaGcYCQpqDsNdLy8wNoB/nFBYfY41j/38vsMKpo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/backends/null.py</key>
		<dict>
			<key>hash</key>
			<data>
			aKpQxwcfrYLgpjVRyJR4V8YOfXY=
			</data>
			<key>hash2</key>
			<data>
			HW+Ovygh78UebL+ICPTilmCOk37h5WFPvVlMnNP8ElA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/cli.py</key>
		<dict>
			<key>hash</key>
			<data>
			5mcyCPQx8o3X/of4HpaFL7/+cUA=
			</data>
			<key>hash2</key>
			<data>
			B9084Rmlt4atfQCw2qugMmovVQzeFjkeLRf6vTNcMTI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/compat/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			fSJ2DQ6D0UqpYAQ1qnH/u14UXJ8=
			</data>
			<key>hash2</key>
			<data>
			WXWOxJd1wdBdrTNjKqjt8jOmfIahcIipDahbqdlQ6g8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/compat/properties.py</key>
		<dict>
			<key>hash</key>
			<data>
			T/rQtyqPlKAHYH3RyGRGeqxgucs=
			</data>
			<key>hash2</key>
			<data>
			JTlR3v7A5AgK93grI2nIW1sj0efYePgWQURDsWHwzj4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/compat/py312.py</key>
		<dict>
			<key>hash</key>
			<data>
			WnLWTNtg03Kt9UFBrXPIo1f433M=
			</data>
			<key>hash2</key>
			<data>
			euMz5d91tbdrG2JkpoqDu3bBg3Pjzd3pEyWVxSK4IkA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/compat/py38.py</key>
		<dict>
			<key>hash</key>
			<data>
			iPE6uQTOOlUDlalRZ+IZL/AbItg=
			</data>
			<key>hash2</key>
			<data>
			J3+MBpHW4APCrGRqBDTw4aftnr2s2+cVB59B6tOFwkk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/completion.py</key>
		<dict>
			<key>hash</key>
			<data>
			FHmdO0a6sPQCDVGQFXnHd9whnvo=
			</data>
			<key>hash2</key>
			<data>
			HNA+saBnIGH/fZFMH9YYclwkUOSuMpmXJOzx6uKwVK0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/core.py</key>
		<dict>
			<key>hash</key>
			<data>
			Ezf6dy/vkOZr7E2szluZB4TDpc8=
			</data>
			<key>hash2</key>
			<data>
			9h22fRu8+xAX86LBzuZHRXcp6w8SykIBX413juMU66I=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/credentials.py</key>
		<dict>
			<key>hash</key>
			<data>
			xJmgt5DCICk5IAswmzBOTG2TLLo=
			</data>
			<key>hash2</key>
			<data>
			PWFUzeAEX9FqjYonSIST4y6WHqQ2lKceLcvicKSaipY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/devpi_client.py</key>
		<dict>
			<key>hash</key>
			<data>
			geyX76HMmEWYO1hq7DO07Q7FiNI=
			</data>
			<key>hash2</key>
			<data>
			IpkyYAso0BH9tXpsZ3K1UjJG/Obtj6kTflrpDatNzoQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/errors.py</key>
		<dict>
			<key>hash</key>
			<data>
			P9GpfIAyGruE4HWR1ntduCw15k4=
			</data>
			<key>hash2</key>
			<data>
			hiHZxG3e1WABMDw80iT0Yg6qrccaVuVUpTNFK7iVmnY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/http.py</key>
		<dict>
			<key>hash</key>
			<data>
			CGfgFviJCR9jVurWtd+flKpqpXE=
			</data>
			<key>hash2</key>
			<data>
			udH83q5BIrfKYm+4AOuefQ3Avb+J9UbpXBYu49Ik/iA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/py.typed</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/testing/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/testing/backend.py</key>
		<dict>
			<key>hash</key>
			<data>
			NfL/t1Rc/i9CwngV6JjsYucI3Q4=
			</data>
			<key>hash2</key>
			<data>
			HuCE8NL1rXMIZBrFELce2aO+N5pY3UEtQLDsNdCgvyA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/testing/util.py</key>
		<dict>
			<key>hash</key>
			<data>
			SrSAknM13y9xFYUz1A90K9enn5Q=
			</data>
			<key>hash2</key>
			<data>
			O15JsfcLIBcnsF1O8LfnbWkeEuiEfbovzQ1h8oN7XUA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/util/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			7G4yJTd5ohFKW56A3ILQnoIbrSM=
			</data>
			<key>hash2</key>
			<data>
			ilEB7cz4cWl7acmrubGF9142ZeBer1mFqaL0U+7UXAc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/keyring/util/platform_.py</key>
		<dict>
			<key>hash</key>
			<data>
			Akkn7UDUpTFKt31xDIqIhZWo/PU=
			</data>
			<key>hash2</key>
			<data>
			lhsGKWZobEvsztNOkotUoNqiHUhJ7G4ENCfdDwp2wVA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_asyncio.so</key>
		<dict>
			<key>hash</key>
			<data>
			dcjEbBLE0lZpr4CY12xZ3oXHNrY=
			</data>
			<key>hash2</key>
			<data>
			n78hkj4udxz9pw5GvAoYOCrXkq9MbE1S6/fHG0iyXnU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_bisect.so</key>
		<dict>
			<key>hash</key>
			<data>
			rMlB6WufLhsKArU4xfZ3elrXxW8=
			</data>
			<key>hash2</key>
			<data>
			uBHJMpQirAZPxoJ8XF/uA7rbxSMaxvn11H5z3OG2XL4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_blake2.so</key>
		<dict>
			<key>hash</key>
			<data>
			CMqEBfs7iJ75tw/YLOttRmizoaU=
			</data>
			<key>hash2</key>
			<data>
			BqUTrZzJbtZ+5DaQYoi5NYb4wlg6Ol3AZMkbEM9FMWA=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_bz2.so</key>
		<dict>
			<key>hash</key>
			<data>
			LupBGav6Pp/OZzJ+yTqwQppo+rE=
			</data>
			<key>hash2</key>
			<data>
			ktKhZjajKJY2dDTSnSs7uTh0Nv/ygYiD5eeXzY/mVHM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_cn.so</key>
		<dict>
			<key>hash</key>
			<data>
			JC2tRm+PZ589hGQpVNMS6vM/ZO8=
			</data>
			<key>hash2</key>
			<data>
			yCJvHEoDq/UGg5d8AbIiHi+em2CAWY0zR0787UWb4Io=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_hk.so</key>
		<dict>
			<key>hash</key>
			<data>
			7HmDykqEvdeB1x4Yk63JUNNQk/M=
			</data>
			<key>hash2</key>
			<data>
			45LVYASediIiXIFNd6QizxoVbSskje4Jb0CTMcU5CVg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_iso2022.so</key>
		<dict>
			<key>hash</key>
			<data>
			8lhrgEjgIMAqouHvO7Ftu7ylILo=
			</data>
			<key>hash2</key>
			<data>
			1VltYtiDc6cFE4IZc4Wgx8Y5TLooI0YKLTnTZsdE3a0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_jp.so</key>
		<dict>
			<key>hash</key>
			<data>
			RbCUvlcD3khoLffULu0K9MHbB7I=
			</data>
			<key>hash2</key>
			<data>
			8Sy4hL3KjEVfzNK3zyijYOzBZesJr3s9czVIbT+g1Jk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_kr.so</key>
		<dict>
			<key>hash</key>
			<data>
			1h37IL8NPpjCG4atqtvVkTNTfrI=
			</data>
			<key>hash2</key>
			<data>
			f0gqcvws6plxMgVV6+E1Ota4KKJ8DEjFzfs1xkkASBs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_codecs_tw.so</key>
		<dict>
			<key>hash</key>
			<data>
			qJdcFd5l9iPam10p7rkdE2jv9I4=
			</data>
			<key>hash2</key>
			<data>
			BBO56CpOLuI0Y3s4ToduRkwM2eO4CcR39yN+TX1t7PY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_contextvars.so</key>
		<dict>
			<key>hash</key>
			<data>
			FXpNTmau1vsBHNeCSkU21kENyFE=
			</data>
			<key>hash2</key>
			<data>
			gc1EzjS7n7u2ItvaXkdY53lP+3sTnVJumh0HTzHteZg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_csv.so</key>
		<dict>
			<key>hash</key>
			<data>
			/NSaQhZWFt/+jilkDEgzlFhnfR0=
			</data>
			<key>hash2</key>
			<data>
			ctNCNERnOAa+zBrsuCExEvL+py+A1MQmYbI1oOfq6vY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_ctypes.so</key>
		<dict>
			<key>hash</key>
			<data>
			10+zm89aeOYnmaDno8nn87i2/as=
			</data>
			<key>hash2</key>
			<data>
			bN46AwHyuG2YgjllDrMw5jbwIQnJF512A+LGps7dyIM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_datetime.so</key>
		<dict>
			<key>hash</key>
			<data>
			gN6xUyYcQWodO0Jnasuw85mcsrI=
			</data>
			<key>hash2</key>
			<data>
			HCeOCY5Vm0zofuUJjU7Ruh4wPxGRsjskok8CXPUko2c=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_decimal.so</key>
		<dict>
			<key>hash</key>
			<data>
			7KkHVfRM8Er67D/7N6M2/6ohBGw=
			</data>
			<key>hash2</key>
			<data>
			rIYJ0AZhuXbSsQ/ciO7k4Nu9BgQQuEzAHxAkfeN12b8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_elementtree.so</key>
		<dict>
			<key>hash</key>
			<data>
			LnQ51rGZIONwyDUDRYG61lki6pE=
			</data>
			<key>hash2</key>
			<data>
			ON9N0dyS6xSi+pZ/MTOghhfdTR51ZdqEeoFqYraWFYs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_hashlib.so</key>
		<dict>
			<key>hash</key>
			<data>
			qfCZ0uMDDTdoypDAEf2BROub9vo=
			</data>
			<key>hash2</key>
			<data>
			bF6Mz8UHnGk7s2kK1+7nJ2Hdug1Mw3jxQwWl2rfnyDM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_heapq.so</key>
		<dict>
			<key>hash</key>
			<data>
			pqZC+W8N4PXZ5y9z/76qO6FxDJ4=
			</data>
			<key>hash2</key>
			<data>
			1HYhWsAtN309evYE44J2qR9yGf2JKLwDFQrDojhS2OY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_json.so</key>
		<dict>
			<key>hash</key>
			<data>
			SXPERgMWjkd3vSaVpFz3hdyURJo=
			</data>
			<key>hash2</key>
			<data>
			qQexj8f03bbsIYVeq+pIHJ/P8EFXdw3HICigfaGUTxQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_lzma.so</key>
		<dict>
			<key>hash</key>
			<data>
			HUbGnAGFarAVQLi7xjXgI5rpOhQ=
			</data>
			<key>hash2</key>
			<data>
			jr7x3+YWPg8mWsTtTbqbIXp+MBBTfGfodjH42610p8U=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_md5.so</key>
		<dict>
			<key>hash</key>
			<data>
			k8jQFVKHd5u9Wo9GbsJ9zHkanRA=
			</data>
			<key>hash2</key>
			<data>
			lydK6611SxskXBUbzOVf6Df1j0ZBoX3mvPPAgnVmT80=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_multibytecodec.so</key>
		<dict>
			<key>hash</key>
			<data>
			tRR9PRdiPx/HD6UpIB9xaNSIYMk=
			</data>
			<key>hash2</key>
			<data>
			9ZMr6QtrrfoS5xoWXw32ZRkIdk2gdqtA2iTxnKKX4IE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_multiprocessing.so</key>
		<dict>
			<key>hash</key>
			<data>
			Dq2S7Fb8CHnCExIjzaVKFdbMBiY=
			</data>
			<key>hash2</key>
			<data>
			Kn/5Pn6xtrCc15xo4+OD2FISq2kGIs7rz+LzfXQ2sjQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_opcode.so</key>
		<dict>
			<key>hash</key>
			<data>
			bHy5c25keGrZwBlTakrrK8Cz8g4=
			</data>
			<key>hash2</key>
			<data>
			WmkrQ81OfHAOxq8yzIHDbOMynjRb7hZNrZF4vesz7z4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_pickle.so</key>
		<dict>
			<key>hash</key>
			<data>
			1w6ytvRxbjzUxaT1Jh9OUuqrI3s=
			</data>
			<key>hash2</key>
			<data>
			nJ27zc7uCPeI/GkX/uKnUoV72jirI9LKMZWkKPoXDP0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_posixshmem.so</key>
		<dict>
			<key>hash</key>
			<data>
			qfShPtf/0UmlEtlk0OqexfimOcA=
			</data>
			<key>hash2</key>
			<data>
			4Zw+rOs0/7fs6kREzmJigg2QefUpYIYFQJFCOkxwxEI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_posixsubprocess.so</key>
		<dict>
			<key>hash</key>
			<data>
			47d8JStsRsMugp9KL60BiZjXOIg=
			</data>
			<key>hash2</key>
			<data>
			UT0t5o874SkaiE9//+xvEkamonj6yZYsFe1488jL+H4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_queue.so</key>
		<dict>
			<key>hash</key>
			<data>
			x+wY4s4nSkPoTL8aNkMPVlwSFBs=
			</data>
			<key>hash2</key>
			<data>
			pDQf3UQ2H+rR/u5UIe+eYRUzbvzPGeJvd8Lz+OYqUi4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_random.so</key>
		<dict>
			<key>hash</key>
			<data>
			nAZRnGKApc0cYx9mozFaXIOtnRk=
			</data>
			<key>hash2</key>
			<data>
			i9bJ1p5pfgbXsY6dcSUPJYdpWEZ6xkRqtSQoJ7jq5Lw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_scproxy.so</key>
		<dict>
			<key>hash</key>
			<data>
			yAycvYZhpNgYLQ/nZ2GyRJiYv/E=
			</data>
			<key>hash2</key>
			<data>
			AqG78p/6ywVrlyv7kjN1DejGwASa/u3PPirrUrjfFWk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_sha1.so</key>
		<dict>
			<key>hash</key>
			<data>
			CmPtUfPLnhPTOlwPLN74RmWl3I0=
			</data>
			<key>hash2</key>
			<data>
			O2RFEny6mrea81iSj4vTrD2BpFjEs67dh/kZIymTB2U=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_sha256.so</key>
		<dict>
			<key>hash</key>
			<data>
			UASFPBUWwBfx80/CoTdXcSZeprM=
			</data>
			<key>hash2</key>
			<data>
			03ANEH4lvMrRiThmNU4845aTdXqq7+dvOX3VUW9DBCY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_sha3.so</key>
		<dict>
			<key>hash</key>
			<data>
			T2GIJodMuH3CqS7IcoZEDWghgYw=
			</data>
			<key>hash2</key>
			<data>
			d8gGB5s05KU2eVLPpv/tDTQBUkUCtYERvOzCiX1WQ/c=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_sha512.so</key>
		<dict>
			<key>hash</key>
			<data>
			AZUByakSC0XPT3AhQQT24je0KFY=
			</data>
			<key>hash2</key>
			<data>
			Na7n99WiXWl3veBm88+DcbpQkTOhnN4FpwCCNCiXmss=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_socket.so</key>
		<dict>
			<key>hash</key>
			<data>
			MyJDzuWUGSytSEZAALeQMoX+5MQ=
			</data>
			<key>hash2</key>
			<data>
			l1r3Ahw5vENtyHyrgAglISspRLaDfEx0FFrW9vb1294=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_ssl.so</key>
		<dict>
			<key>hash</key>
			<data>
			8YozUEmN/CPpkpZJFDQBzJ5rCAQ=
			</data>
			<key>hash2</key>
			<data>
			qeGTEO/w6xc3iznwZkWR4s78RCeFcFCVDMBYH7az0oE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_statistics.so</key>
		<dict>
			<key>hash</key>
			<data>
			Ag93je9thyGtXlWN5qX1TqBe4P8=
			</data>
			<key>hash2</key>
			<data>
			TzrVHUjDuHFxvKtg+vjAXursCKHeQ+m1aD+ctv/UsHs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_struct.so</key>
		<dict>
			<key>hash</key>
			<data>
			6CTH0gbXOyu0X/HYEcum6QxKQkg=
			</data>
			<key>hash2</key>
			<data>
			95cemt5T52beMMO9DynIUuPjFmGTBRE7IFJ8vB6Ii7c=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/_tkinter.so</key>
		<dict>
			<key>hash</key>
			<data>
			6DuUphwoSOti8YC+lghd93eMgu4=
			</data>
			<key>hash2</key>
			<data>
			KmF/t4dj/gOjadLUzw+hI8cy0aC+bzWM90Ul+zH31Xs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/array.so</key>
		<dict>
			<key>hash</key>
			<data>
			q57fiWikohrTaY8Qmh5JAXsZnmc=
			</data>
			<key>hash2</key>
			<data>
			7Icfd3JXkjqL6nlUNCEpXayxbfVvuL6OZ01N0+FEI28=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/binascii.so</key>
		<dict>
			<key>hash</key>
			<data>
			bIb1Dj9/lgl8UGBbW+QawTCUhB4=
			</data>
			<key>hash2</key>
			<data>
			VdSRNVS7mHg74nW/UIYOlqcS1lpJ0L7hBea8OIKHqVI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/grp.so</key>
		<dict>
			<key>hash</key>
			<data>
			es0fXdLedoVrqPxPPHfC0yhkUC4=
			</data>
			<key>hash2</key>
			<data>
			GtnmWhSRG2MskCw+7YPmydye9jqYYcXmScm8QQCrRpw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/math.so</key>
		<dict>
			<key>hash</key>
			<data>
			qc699vXMUofZv2mcEbgRbmQuLXA=
			</data>
			<key>hash2</key>
			<data>
			zI9eAzvJUrN3kgs7IJTg9FDH6lPBAEdxA21IqzqF+eE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/mmap.so</key>
		<dict>
			<key>hash</key>
			<data>
			zvptJJTW68v8266GyePc3aSWgds=
			</data>
			<key>hash2</key>
			<data>
			YZUDNHiOX0Ozlnp2y0XstqzTRuD7XUGhNIhdYkhC3W4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/pyexpat.so</key>
		<dict>
			<key>hash</key>
			<data>
			j5KBHjGSTSlFkzj/p9ko3R1dloM=
			</data>
			<key>hash2</key>
			<data>
			AKZKH7xqdIRP29LkIofVerfAO1SAK7YoNL9JVOZxEmQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/resource.so</key>
		<dict>
			<key>hash</key>
			<data>
			cTRGLNnBFkrCnh+9zrXQ4hWiV1c=
			</data>
			<key>hash2</key>
			<data>
			XrEpKHgc0i5AqaaxoIE6LvzfE6h6stQ5RNRuSNfzQDM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/select.so</key>
		<dict>
			<key>hash</key>
			<data>
			TJ5h+5rEEsmGJw6FNTjJj2sz704=
			</data>
			<key>hash2</key>
			<data>
			ZgMTfPbwS7hDUBNWZ5F4w8ODvAlIUgDPpN0VzZPyBM0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/termios.so</key>
		<dict>
			<key>hash</key>
			<data>
			ocaRQxaIXTyXK7SREVIeMiP4jbo=
			</data>
			<key>hash2</key>
			<data>
			9I6E1C44wyY1W+IlNXR/UEtSNxSNtDrKiZZzHtjb+pw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/unicodedata.so</key>
		<dict>
			<key>hash</key>
			<data>
			6nftJMiVVGV9Sl21+uL6kbAy6+k=
			</data>
			<key>hash2</key>
			<data>
			mY44UgAu003mn9S3NpcuETL9pm8cUOUOhqdqK5UORCI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/lib-dynload/zlib.so</key>
		<dict>
			<key>hash</key>
			<data>
			k8OPcRYfPw/B/LpS48/6+w2yOfE=
			</data>
			<key>hash2</key>
			<data>
			LnIl/79RxApOI5fsSl+BlHuYjMJbURtzSq0B30eoOLM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pathlib.py</key>
		<dict>
			<key>hash</key>
			<data>
			eT8N7Ac9mO/G8569COkmtZVGrlM=
			</data>
			<key>hash2</key>
			<data>
			azPS1VwJqpxgelKAA+2P4VSJSvqpd+xVTgqFgkBBK3I=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			pZR23JQpotio4n8yh/o3ncCVAsw=
			</data>
			<key>hash2</key>
			<data>
			uxrWmKF3lxsG4q6ojHlu4tB8j8Kw9jqx/SNMyDKP5q4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/api_tests.txt</key>
		<dict>
			<key>hash</key>
			<data>
			5kbHVTOUa3XReBiUNgr1GnGlYBw=
			</data>
			<key>hash2</key>
			<data>
			XEdvy4igHHrq2qNHNMHnlfO6XSQKNqOyLHbl6QcpfAI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/py.typed</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package-source/setup.cfg</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package-source/setup.py</key>
		<dict>
			<key>hash</key>
			<data>
			YzPI3SI98w9lIahP7jnI1NBl8EU=
			</data>
			<key>hash2</key>
			<data>
			1VobhAZbMb7M9mfhb/NE8PwDsvukoWLs9aUAS0pYhe8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip</key>
		<dict>
			<key>hash</key>
			<data>
			Ppo7Vi7IEYtwOiI8WhWVfbIQBHs=
			</data>
			<key>hash2</key>
			<data>
			AYRcQ39GVePPnMT8TknP1gdDHyJnXhthESmpAjnzSCI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO</key>
		<dict>
			<key>hash</key>
			<data>
			bXpnwYYTvkslWBIRG0YZ3peavCg=
			</data>
			<key>hash2</key>
			<data>
			JvWv9Io2PAuYwEEw2fBW4Qc5YvdbkscpKX1kmLzsoHk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt</key>
		<dict>
			<key>hash</key>
			<data>
			TJ2MjjVWRax3KqI3UIcpDte2NXs=
			</data>
			<key>hash2</key>
			<data>
			4ClkH8eTovZrdVrJFsVuxdbMEF++lBVSuKonDAPE5Jc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt</key>
		<dict>
			<key>hash</key>
			<data>
			rcg7GeeTSRscbqD9i0bNnzLlkvw=
			</data>
			<key>hash2</key>
			<data>
			AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt</key>
		<dict>
			<key>hash</key>
			<data>
			rcg7GeeTSRscbqD9i0bNnzLlkvw=
			</data>
			<key>hash2</key>
			<data>
			AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe</key>
		<dict>
			<key>hash</key>
			<data>
			rcg7GeeTSRscbqD9i0bNnzLlkvw=
			</data>
			<key>hash2</key>
			<data>
			AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg</key>
		<dict>
			<key>hash</key>
			<data>
			nUk3q6cqxlg+uWEUYFeL3QtXoCU=
			</data>
			<key>hash2</key>
			<data>
			ZTlMGxjRGiKDNkiA2c75jbQH2TWIteP00irF9gvczbo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_find_distributions.py</key>
		<dict>
			<key>hash</key>
			<data>
			CVpgQ1bXFyNsPUwRhPqB5RuE58I=
			</data>
			<key>hash2</key>
			<data>
			U91cov5L1COAIWLNq3Xy4plU7/MnOE1WtXMu6iV2waM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_integration_zope_interface.py</key>
		<dict>
			<key>hash</key>
			<data>
			kOefkG+CjQINQqEmITFENZQBfRg=
			</data>
			<key>hash2</key>
			<data>
			nzVoK557KZQN0V3DIQ1sVeaCOgt4Kpl+CODAWsO7pmc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_markers.py</key>
		<dict>
			<key>hash</key>
			<data>
			cPy9DVAWGZx5I00CxSoNhKMoDnw=
			</data>
			<key>hash2</key>
			<data>
			0orKg7UMDf7fnuNQvRMOc+EF9EAP/JTQnk4mtGgbW50=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_pkg_resources.py</key>
		<dict>
			<key>hash</key>
			<data>
			sajuwdZrAGOSB9LLPp1iTOK0kkM=
			</data>
			<key>hash2</key>
			<data>
			5Mt4bJQhLCL8j8cC46Uv32Np2Xc1TTxLGawIfET55Fk=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_resources.py</key>
		<dict>
			<key>hash</key>
			<data>
			aMg7adI9kZQLSQigZv6XBWul8jY=
			</data>
			<key>hash2</key>
			<data>
			K0LqMAUGpRQ9pUb9K0vyI7GesvtlQvTH074m+E2VQlo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/pkg_resources/tests/test_working_set.py</key>
		<dict>
			<key>hash</key>
			<data>
			GFG7sdHW07sfzDTHzhq2Iw0dGe4=
			</data>
			<key>hash2</key>
			<data>
			lRtGJWIixSwSMSbjHgRxeJEQiLMRXcz3xzJL2qL7eXY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/platform.py</key>
		<dict>
			<key>hash</key>
			<data>
			R/OnehVJzdSVo81ARA6312Sa5GU=
			</data>
			<key>hash2</key>
			<data>
			LQ+Q2CH9mYI46bcWtk+y5zuMrXN1xO/5nUlzBVMMt9k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/queue.py</key>
		<dict>
			<key>hash</key>
			<data>
			fUCeNg0y8WEBGMSaMP28IV/qAc4=
			</data>
			<key>hash2</key>
			<data>
			ncpyeI5qYaAov8pZPVuWm7YkbaCAuVU+1vjjLk0p39k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/site.pyc</key>
		<dict>
			<key>symlink</key>
			<string>../../site.pyc</string>
		</dict>
		<key>Resources/lib/python3.9/socket.py</key>
		<dict>
			<key>hash</key>
			<data>
			srrtvxGy7x3uVsxGJN8gClj6v1E=
			</data>
			<key>hash2</key>
			<data>
			+NtayW3LJRzhZ4/Ltk5WsmPDfB5WPN5hkSZrvwun92Y=
			</data>
		</dict>
		<key>Resources/lib/python3.9/subprocess.py</key>
		<dict>
			<key>hash</key>
			<data>
			Ex0sC4kIbI8hWlnxNI/6WyhQcV8=
			</data>
			<key>hash2</key>
			<data>
			0vc8cSijkx90QRtGNSFGuoUyKo3I8SpyHUgB3EPZdm4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/threading.py</key>
		<dict>
			<key>hash</key>
			<data>
			9TR/PEGkLRFsjjIhl2C0ENu0EGk=
			</data>
			<key>hash2</key>
			<data>
			V2CFtNCbSIEz6QC83IIdoAXT8RfdNZXPjDktxShXKfI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			0MW+CGUqz7tZTBzAYrEzVx0zZ/w=
			</data>
			<key>hash2</key>
			<data>
			OCvIEqTLGFGQqwvAYPjLRw/Pxv/dLzcFeUPbkAalC3A=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/__main__.py</key>
		<dict>
			<key>hash</key>
			<data>
			p3f6Ybk1seXMnsaMZvHqyKhXbuY=
			</data>
			<key>hash2</key>
			<data>
			lzimy5zdgTlyHdghGL1SeJfbUyXYByIog/cPscWhwn4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/colorchooser.py</key>
		<dict>
			<key>hash</key>
			<data>
			TnuKeJ2LdbJw89UIaqTQF3TIRTY=
			</data>
			<key>hash2</key>
			<data>
			EiQkHc+07Gr/PK/Gat6ysqN1k5eihpMXORVFjFAEAUM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/commondialog.py</key>
		<dict>
			<key>hash</key>
			<data>
			a4EyG4xEffUSCKetuFHsT9PfOJY=
			</data>
			<key>hash2</key>
			<data>
			rQcEyhAbp31lSuQrP3nT08AEZPOuYfiddG3hg4K3sys=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/constants.py</key>
		<dict>
			<key>hash</key>
			<data>
			/B7xu2NqfWUwfm+fw6c5oH34luM=
			</data>
			<key>hash2</key>
			<data>
			wBMU3FHRyO/+uiUocgpl2hM1ltQUMgDGhZXAIGe/HaI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/dialog.py</key>
		<dict>
			<key>hash</key>
			<data>
			g+BVF0qNJJi+C7eqTWfZmoKWh5A=
			</data>
			<key>hash2</key>
			<data>
			beOVtMa/LMueLJAv70onWs9ggm9pF9JUd+7jNIEsvdE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/dnd.py</key>
		<dict>
			<key>hash</key>
			<data>
			EwuL5jfgyqqgTMcityoNPck52TI=
			</data>
			<key>hash2</key>
			<data>
			qUaigwjsvmU0816UUhVNw82Yf+jB3S7+LAYm+gSyFyo=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/filedialog.py</key>
		<dict>
			<key>hash</key>
			<data>
			4q2l1IlpnVIYWXqO73LZ07VJxhU=
			</data>
			<key>hash2</key>
			<data>
			d6ehMFcsLwNR8fDewNtOesnnGXC3Tfy2wtk7ZtqGnfY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/font.py</key>
		<dict>
			<key>hash</key>
			<data>
			HSD8nDaD6lU+ymQe3f1W8QxD+4w=
			</data>
			<key>hash2</key>
			<data>
			y4NTS6I1kcaQaWIXOzUz6UM2acBGV1lacmsCbHa7lvI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/messagebox.py</key>
		<dict>
			<key>hash</key>
			<data>
			VJcUvUzf8VE7IAb7I/ch8EubU00=
			</data>
			<key>hash2</key>
			<data>
			zb9lXGZ3ihnw4ldUpfGYqFDIvZWM5lHo/ksrUq1/nGM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/scrolledtext.py</key>
		<dict>
			<key>hash</key>
			<data>
			nrnBvmSIrJ2rO5NpFXS2JckZQwc=
			</data>
			<key>hash2</key>
			<data>
			x8wFDsnMPMakchW1vHmy0+XG7YlaQwCrDiD2wkk4Xj8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/simpledialog.py</key>
		<dict>
			<key>hash</key>
			<data>
			ne8dP+ZvTlMvwIboL2b1mJoEfAo=
			</data>
			<key>hash2</key>
			<data>
			sCRpfpYP19UyYT3xhBw1sIP+d8frcIW6D+0+gk2j9Uw=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/README</key>
		<dict>
			<key>hash</key>
			<data>
			zeWBu5/aaPO06CqQtjGNqBzH3Rs=
			</data>
			<key>hash2</key>
			<data>
			/jx51dqGFso396nY/dqsLJFktZPHsRZYCqmWkKX1mrU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			oBgYes7z6WsNBBcvpV0BOB/Oks0=
			</data>
			<key>hash2</key>
			<data>
			yCBM/Tcu5F95y3RO0FZbyNSGcWEVpUb0hHfAcZuKa7k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/runtktests.py</key>
		<dict>
			<key>hash</key>
			<data>
			MrAZCMWJIelT7Din9ckqF58wLqU=
			</data>
			<key>hash2</key>
			<data>
			3OU7i/r0OVv6LUXfhjQOuzpTnTFSq+GVNIbWzqCbW5s=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/support.py</key>
		<dict>
			<key>hash</key>
			<data>
			un1n/mSt7XPmcLhZipiNFS5YqZQ=
			</data>
			<key>hash2</key>
			<data>
			bHhII31JDFcE2L+kfgvtCRxRcezOv7q8eD84RrWqV60=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			oBgYes7z6WsNBBcvpV0BOB/Oks0=
			</data>
			<key>hash2</key>
			<data>
			yCBM/Tcu5F95y3RO0FZbyNSGcWEVpUb0hHfAcZuKa7k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_colorchooser.py</key>
		<dict>
			<key>hash</key>
			<data>
			oWu9ewDSooLDuMzQBWkwMUkOgNU=
			</data>
			<key>hash2</key>
			<data>
			tNzynJ94KqxhGowqud5Vzx/TrSw3wleZnW3cbF8yeHc=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_font.py</key>
		<dict>
			<key>hash</key>
			<data>
			/LgsY1TC1BudvlzRvueSlXVj4hg=
			</data>
			<key>hash2</key>
			<data>
			YdMG9cbiJ/FywlSm7MMgIiroxCW4FPtE2CmKQu1L3KY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_geometry_managers.py</key>
		<dict>
			<key>hash</key>
			<data>
			sxj0SspjZcGk5ei62BaPUCMvA7k=
			</data>
			<key>hash2</key>
			<data>
			UDzuiep8eMTfQQfr1fO5nQlciwyxMfduA/T1QQNjjiQ=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_images.py</key>
		<dict>
			<key>hash</key>
			<data>
			Nk4XWDcryv/R9GK679v0n871yCg=
			</data>
			<key>hash2</key>
			<data>
			qiKMPwaCp1i0uyJrZyxUxuLVntd8J7nfyDpS6El497Q=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_loadtk.py</key>
		<dict>
			<key>hash</key>
			<data>
			2ZM/FetRiQZZseW17oVJ93E5YZk=
			</data>
			<key>hash2</key>
			<data>
			d4xxRDSg5cCwGKMBKUCEFOe3I3Bfj2RQBA0oAqCYOCg=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_misc.py</key>
		<dict>
			<key>hash</key>
			<data>
			mHmQm4BEWwrKJcDnmf3pum9Vkvw=
			</data>
			<key>hash2</key>
			<data>
			kMFkaDLsLNaUbh7J/+ocCtQF9aLbD2qelJTkqPbpllI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_simpledialog.py</key>
		<dict>
			<key>hash</key>
			<data>
			1HPHBNEGDhWGdMwKu4N+xGDUiRM=
			</data>
			<key>hash2</key>
			<data>
			OLp7OK1zyz7hjwqXxG9thNNkxBz/0anNLIAloiheZNU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_text.py</key>
		<dict>
			<key>hash</key>
			<data>
			WV9/R2CJqT2V24ZQoQ6+JvIEmgM=
			</data>
			<key>hash2</key>
			<data>
			OrXrSwClnnpH5HfAlp+TnF8BAGZp6+wxII40rRFPJM4=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_variables.py</key>
		<dict>
			<key>hash</key>
			<data>
			8fzykfVAsT+HaviDxz8olKKI/bs=
			</data>
			<key>hash2</key>
			<data>
			ZFhAW67bgj/IQD1k6ZLidu6K+GQYIa0SKQl/F4UGdOE=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_tkinter/test_widgets.py</key>
		<dict>
			<key>hash</key>
			<data>
			lO/pVgs/9ApmqdMJMMDWDDvdC08=
			</data>
			<key>hash2</key>
			<data>
			im5Pep9GoObHG+mRAzKrbCMyT7JjnwYznZFMvThYC8A=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/__init__.py</key>
		<dict>
			<key>hash</key>
			<data>
			oBgYes7z6WsNBBcvpV0BOB/Oks0=
			</data>
			<key>hash2</key>
			<data>
			yCBM/Tcu5F95y3RO0FZbyNSGcWEVpUb0hHfAcZuKa7k=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_extensions.py</key>
		<dict>
			<key>hash</key>
			<data>
			vjCuWw4/Dh4kDcuf6fn/xJm6xsM=
			</data>
			<key>hash2</key>
			<data>
			jlvyOoS65UhF106WKi0sY4waKbpadYrF95AD/fSo8co=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_functions.py</key>
		<dict>
			<key>hash</key>
			<data>
			uH+dqYccsx1rdEhAjRO+R/Qnzdc=
			</data>
			<key>hash2</key>
			<data>
			fYvlOYXeEK5Z5nUENFZF1ufg0Pm/ky4tSpB3stlUJeY=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_style.py</key>
		<dict>
			<key>hash</key>
			<data>
			1gQDhQLRoNsHkhWhjqIbEMjcl/w=
			</data>
			<key>hash2</key>
			<data>
			WS7mA4/l3319leon7ZuLrPKdcRSdUjHRJ8HAeYaAA94=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/test_ttk/test_widgets.py</key>
		<dict>
			<key>hash</key>
			<data>
			p3IjjUbYMOCmtxBeIGSMBMsfSus=
			</data>
			<key>hash2</key>
			<data>
			CBLDekrRlU+oKz5G8EfRTruNOkFx8ZLtXRSgfVmkLMU=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/test/widget_tests.py</key>
		<dict>
			<key>hash</key>
			<data>
			ofg6mYDjGgms61oKTV0qFpNWQBo=
			</data>
			<key>hash2</key>
			<data>
			TR1bpaZMqsayWtmNu3d3W3Dt2v6JDl6wcSPSeKFjpu0=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/tix.py</key>
		<dict>
			<key>hash</key>
			<data>
			gLFCdBrF6wKix+LeQspBxB3PPyY=
			</data>
			<key>hash2</key>
			<data>
			mMP/TYC+rwwtLjzXfBqSaQaaGw7Ob/joQGbGapvp4ro=
			</data>
		</dict>
		<key>Resources/lib/python3.9/tkinter/ttk.py</key>
		<dict>
			<key>hash</key>
			<data>
			YjC5kvG4p6M6BFRnbYPbUR69mp4=
			</data>
			<key>hash2</key>
			<data>
			OwHL1Jdqd1D9soak/HcY/YYvSoKdgPT30igUDXJ7JZM=
			</data>
		</dict>
		<key>Resources/lib/python3.9/xml/05-16 test.xml</key>
		<dict>
			<key>hash</key>
			<data>
			oCrSTZl90lrfp0sIezfZ0yEc0l8=
			</data>
			<key>hash2</key>
			<data>
			ETMDoHKhN8rUPDvfDwP/rQ/Gq80briAr5oiqYqfGk6s=
			</data>
		</dict>
		<key>Resources/lib/python3.9/xml/8123340.xml</key>
		<dict>
			<key>hash</key>
			<data>
			pGAOUdCdXNCVjEFxbiHXj4Np5dA=
			</data>
			<key>hash2</key>
			<data>
			+KJqwa6ljhsgBZnHQrq3DX6weuzUeh3k6dzAvu5ag40=
			</data>
		</dict>
		<key>Resources/lib/python3.9/xml/8123456.xml</key>
		<dict>
			<key>hash</key>
			<data>
			dx/ez0+pzADCIoeErZ4xC7+4z4I=
			</data>
			<key>hash2</key>
			<data>
			tfu8h4Qn7pR/W6uPxY0CnBth/JI9OUvU4jUF0ITj1SI=
			</data>
		</dict>
		<key>Resources/lib/python3.9/xml/reports (1).xml</key>
		<dict>
			<key>hash</key>
			<data>
			XbdKR+/YVse8PU3DzelmFigldr8=
			</data>
			<key>hash2</key>
			<data>
			5cefcTdr8ZTk28szPsu9MUOaQ1m9byeo/ZwzehmJjn8=
			</data>
		</dict>
		<key>Resources/lib/python3.9/xml/reports.xml</key>
		<dict>
			<key>hash</key>
			<data>
			f8plR/Z28+YSBLAp/ubwjwrka7o=
			</data>
			<key>hash2</key>
			<data>
			vWj484ytl9qRKVB8o0fa8yC9LzefN06omu4twep4iLM=
			</data>
		</dict>
		<key>Resources/lib/python39.zip</key>
		<dict>
			<key>hash</key>
			<data>
			K9eNk3HhCneCszwaIMDSGH1YcV0=
			</data>
			<key>hash2</key>
			<data>
			vAZgeZDoZw9zEh04fc2o9ZFMhJiO/yQrDbont0Dp9qI=
			</data>
		</dict>
		<key>Resources/openssl.ca/cert.pem</key>
		<dict>
			<key>hash</key>
			<data>
			pozv5aCs1VeWKDdWZs+5LigONIM=
			</data>
			<key>hash2</key>
			<data>
			na6NduVcsImR8rZy1YmZ6hVWDZEHWcFrVE+EO9/7uZQ=
			</data>
		</dict>
		<key>Resources/site.pyc</key>
		<dict>
			<key>hash</key>
			<data>
			EBeYGtd9sFxUNKYLuyif8x/bW6w=
			</data>
			<key>hash2</key>
			<data>
			QaQyvodD0iyyHMODFgl8+P4R+mf6dv8N5zlCQSY2/rc=
			</data>
		</dict>
		<key>Resources/vs_mac_tool_v2.icns</key>
		<dict>
			<key>hash</key>
			<data>
			6DLidyRNkn4oTIyC6LI0fS/sDMM=
			</data>
			<key>hash2</key>
			<data>
			lrPqU4byxTwkOHY4msSrYvfC+sUcrU7P0gZ68v7Cn2E=
			</data>
		</dict>
		<key>Resources/vs_mac_tools_v2.1.0.py</key>
		<dict>
			<key>hash</key>
			<data>
			azxBUVNy+vb1DZDucpdGjgCWM4A=
			</data>
			<key>hash2</key>
			<data>
			cACWkjVRPsYg4CwauUiAv98mQAxuUZiQ4CYTY1elIvs=
			</data>
		</dict>
		<key>Resources/zlib.cpython-39-darwin.so</key>
		<dict>
			<key>hash</key>
			<data>
			k8OPcRYfPw/B/LpS48/6+w2yOfE=
			</data>
			<key>hash2</key>
			<data>
			LnIl/79RxApOI5fsSl+BlHuYjMJbURtzSq0B30eoOLM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
