# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['vs_mac_tools_v2.1.0.py'],
    pathex=[],
    binaries=[],
    datas=[('img', 'img'), ('password_dialog.py', '.'), ('/Users/<USER>/vs-mac-coding/01_Underway/Apple_ADE_checker/venv/lib/python3.9/site-packages/customtkinter/assets', 'customtkinter/assets')],
    hiddenimports=['customtkinter', 'customtkinter.windows', 'customtkinter.windows.widgets', 'customtkinter.windows.ctk_tk', 'customtkinter.windows.ctk_toplevel', 'darkdetect', 'darkdetect._mac_detect', 'PIL', 'PIL._tkinter_finder', 'keyring', 'keyring.backends', 'keyring.backends.macOS', 'password_dialog', 'tkinter', 'tkinter.filedialog', 'tkinter.messagebox', 'xml.dom.minidom', 'xml.etree.ElementTree', 'queue', 'threading', 'socket', 'subprocess', 'platform', 'getpass', 'pathlib', 'csv', 'datetime', 'time', 'os', 'sys'],
    hookspath=['hooks'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VS Mac Tools',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['img/vsmactool.icns'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VS Mac Tools',
)
app = BUNDLE(
    coll,
    name='VS Mac Tools.app',
    icon='img/vsmactool.icns',
    bundle_identifier=None,
)
