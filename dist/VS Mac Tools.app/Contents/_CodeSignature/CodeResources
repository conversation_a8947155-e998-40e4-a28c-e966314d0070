<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/_tcl_data/auto.tcl</key>
		<data>
		PjkX5wd0gYmnreFKHZUFqwwqrhQ=
		</data>
		<key>Resources/_tcl_data/clock.tcl</key>
		<data>
		q95V4+ttJZRv4Hzkrm2WYqfnXQE=
		</data>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<data>
		3YMzPcHIOL65EC8GOXHMwgzE/YA=
		</data>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<data>
		njXr89U4DjS5L+J0QST5MkuQHdM=
		</data>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<data>
		QIEYQ6+V587xeVcQF9f6adFErXg=
		</data>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<data>
		+iJxAw25AF1x+q1gtEdnlV1UMt0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<data>
		YEOW2B/S2Q9XNP5sPyg/jxmqu2Q=
		</data>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<data>
		LiEwDgvIqEfQQjZxsI08ZXYe4XI=
		</data>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<data>
		j/mlJqVF0pOCmmeaLs3TOqb5qQ4=
		</data>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<data>
		SEcFppWWydgT6jYWJcOkXGuzEig=
		</data>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<data>
		Zg2+RYOSPL3/9iYbH630NJZYV5w=
		</data>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<data>
		OP7jn0ThTDohmXj4tuTaVIFSz9Y=
		</data>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<data>
		zBA8U7O6F2RxRYfq69ks0bx1GU0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<data>
		YpTkLtItdWef8UZP9B1D2zsYJMI=
		</data>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<data>
		P3JSRcZgUNOdkjS6rOnQR6OEKUQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<data>
		yV6k7T++8BPYEMC/sZOxX6it57g=
		</data>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<data>
		9DAaE0ChYOHygrX5i/n6y/qTsRk=
		</data>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<data>
		Xq03eI0STU7knsS4qhz2qqnChJw=
		</data>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<data>
		kEs1fDBgPfvPihCgVNk5lgixMd8=
		</data>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<data>
		+L1L9tlfZyy2G47KtYCnZb69rqU=
		</data>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<data>
		iISZ2d/fdcYMJ3A4akUA81dTznA=
		</data>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<data>
		xrHpMg7vRvyaI0N8JV5AheopgNs=
		</data>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<data>
		U1KXcupjIreUnbc+667ZHlpbo9o=
		</data>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<data>
		7yc2cdRoFfIplupjLSLMJ+uMpEs=
		</data>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<data>
		f27Ym9DUFcZNC4oDfwikf+rdFMQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<data>
		4MpAC64PZu6+Tf4UfFoY3TsAt4w=
		</data>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<data>
		DrQP7rijglMLaXSOCL9RMSQjJAM=
		</data>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<data>
		QxJwk50+R5v5uaZj2eZ/zrp5QW8=
		</data>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<data>
		6BSfMzsYCdzN5Rz4tjMhA93n/DA=
		</data>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<data>
		2gV+H5P3VSGlHMcl1HEw9B5QnnA=
		</data>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<data>
		0LPesO5lOc5fKKUUZL+7OqA/KOU=
		</data>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<data>
		Ox0HsCrn47QHhIceF/NjMoNCaOY=
		</data>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<data>
		kEqLeEbTRSFjTIwJAT27HTGvR8o=
		</data>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<data>
		ZgX8yyNaCPkDK7RSMbGmMxdkZks=
		</data>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<data>
		aiHVe0SghWq83mGxwWy5P05MPXQ=
		</data>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<data>
		RlUJxybEloCwI3JQGvelLwmrfVU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<data>
		+PyjmFAJos3Tl8s7rjCK8FsNfKw=
		</data>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<data>
		ic2k/mUVycA1UeThly/UeK86QZw=
		</data>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<data>
		XI5pGuPBMwiCD0z2kgbXZc/VCUs=
		</data>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<data>
		ScZjrCbB/k8P0UKMnvJwWK7mypU=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<data>
		26CcZw8k1HuV0S1LuXBDkbgd2po=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<data>
		OeILQc+osmk3evoG+cTWbt2Uass=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<data>
		lMXzklY2artozWfjAl8Xf1Ts050=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<data>
		nW8HdZilqG5utqTuwUgQv1JfvYk=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<data>
		pxtjgOo9I9wN4R07jOqGpMgGPUc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<data>
		zseHxN54+du5e5xEBwzywSokaPc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<data>
		AcVG7nwQsWSnTWY/pvwrx9MhIVU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<data>
		1GKTSgdO4T8sgQRj/QYQhJU/d7w=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<data>
		YsJTqnqGjOMliYaPqzczZUJFepY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<data>
		SfekKIn7ig14yABnveGAlNvpVu4=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<data>
		UP2mxwoTPLZM84qksvMTtU0v2VU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<data>
		/2LrVxD94RB0qH2u6SKbz39m16A=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<data>
		sL6+3sU/+4lNn7DVfyWrKkWbbdU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<data>
		zBwubDWwBcF+t7Gj10SYOoanVzY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<data>
		+fZLtgFAaOLAc3GGxpS4EB3ZV14=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<data>
		uBBCY1TYV3GMyEHUJNoHDvufFE8=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<data>
		eswpSNXomXUMKsbIFM3hfUIRK8k=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<data>
		TqXsUzFUHt5lqc9gH1QY/Uts/Lw=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<data>
		9YXHClWJ3jlVjawBZ0P/heDF8DI=
		</data>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<data>
		eutwjInBePtNVhHCReoafPZq3zo=
		</data>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<data>
		qSKsrODBpKfdyS/l3XoRbTCjaGs=
		</data>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<data>
		FQE0eHYEY6C841d7TWRuzbB2MrU=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<data>
		NmwTfALgabGpP7tdZLkSDqbprR8=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<data>
		2p5n9k7E9qdMYMtlDVoSxEMNz/c=
		</data>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<data>
		wv+kJ0V7STHlqSMm8lHNPWcQWbA=
		</data>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<data>
		FrUdAXABaIoyy3sV3m56SfKLdv0=
		</data>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<data>
		Y/ROgYKEOE3gerDYsM1vfr/gmrk=
		</data>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<data>
		PeF7KlhmJyYC+46cVJMKTNHzsGw=
		</data>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<data>
		pA5tuX1tsok6Bysiddwi4qTWBzc=
		</data>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<data>
		nCrVPWn1B3hToF8JMzMLXW+IpRw=
		</data>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<data>
		xDQlfXap/fgczNjMFCQsjjlA/Yk=
		</data>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<data>
		uhh8UvrpeS2lv/vqp4H9TgcW4PY=
		</data>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<data>
		5Njqag5wu3kzBMoh6xM3p6LCajE=
		</data>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<data>
		4le9Fu80/cKdW2yYWhtFgBk3NUw=
		</data>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<data>
		Yz0ZC14oHPwBePbBHdchxqJm9kM=
		</data>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<data>
		OC40gkrYt57wyY/VFnUGSf2Usgo=
		</data>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<data>
		pIeUbLLv11/XSFA9deSVcgtT5bw=
		</data>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<data>
		LVaWWyQSXZmdECDHw0e4E6lyZHw=
		</data>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<data>
		8MAUttZ/wNwdG7xfBS8Mixxj2L8=
		</data>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<data>
		RvsXtOCFAQIomg0D9PlWmzqkmU0=
		</data>
		<key>Resources/_tcl_data/history.tcl</key>
		<data>
		f06SrN7NhRXGHAUymopJLjbKE54=
		</data>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<data>
		joZZvvBlr5QwUJu91ftM/g7xQVM=
		</data>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<data>
		YAGligcB3/Il4lEKSq7mSJpTdlc=
		</data>
		<key>Resources/_tcl_data/init.tcl</key>
		<data>
		oQefICjth07c9DR/wCR3l7pHOJU=
		</data>
		<key>Resources/_tcl_data/msgs/af.msg</key>
		<data>
		IImzlI8R74zkvT1XFncVreZYdek=
		</data>
		<key>Resources/_tcl_data/msgs/af_za.msg</key>
		<data>
		Z3OU34HNuvPT5zX0l3FTu1yBsaY=
		</data>
		<key>Resources/_tcl_data/msgs/ar.msg</key>
		<data>
		kINLy9qbkxe5J4bsieINzx8tvSI=
		</data>
		<key>Resources/_tcl_data/msgs/ar_in.msg</key>
		<data>
		dNErTLzfY/3wDlidimBKXFLDk+8=
		</data>
		<key>Resources/_tcl_data/msgs/ar_jo.msg</key>
		<data>
		cJcXux9ipx6U1hBWpwZgxqA7SK4=
		</data>
		<key>Resources/_tcl_data/msgs/ar_lb.msg</key>
		<data>
		rvOKq3NuVDQpXHLBTzgDOq/m7xU=
		</data>
		<key>Resources/_tcl_data/msgs/ar_sy.msg</key>
		<data>
		xrg8AvXUsUBk2Tev2MapK6munvs=
		</data>
		<key>Resources/_tcl_data/msgs/be.msg</key>
		<data>
		dNYj2rYjjQXBjd5X/JVthJdPwtQ=
		</data>
		<key>Resources/_tcl_data/msgs/bg.msg</key>
		<data>
		7FVXoWoCk6v0qo5f1QlAtgqKNqY=
		</data>
		<key>Resources/_tcl_data/msgs/bn.msg</key>
		<data>
		gNsjNoepMUYAMXrTnAFGbGQvPEw=
		</data>
		<key>Resources/_tcl_data/msgs/bn_in.msg</key>
		<data>
		IpYHOujMQheA6KO81YMS1vsvW/w=
		</data>
		<key>Resources/_tcl_data/msgs/ca.msg</key>
		<data>
		jS1T2iCLtnCjNcdS38S0/0UJp5k=
		</data>
		<key>Resources/_tcl_data/msgs/cs.msg</key>
		<data>
		ylxHp2zUUG2OEa7OHqC0plcXYBk=
		</data>
		<key>Resources/_tcl_data/msgs/da.msg</key>
		<data>
		sXHRVUJE0qbtjeF6yAAKoJ0vrek=
		</data>
		<key>Resources/_tcl_data/msgs/de.msg</key>
		<data>
		ar9Rn25IRebxPyctYo3pfy0s1IE=
		</data>
		<key>Resources/_tcl_data/msgs/de_at.msg</key>
		<data>
		t3FK83K0ZioMFd28D4DRJJyx7r0=
		</data>
		<key>Resources/_tcl_data/msgs/de_be.msg</key>
		<data>
		3lGdOobc8ej0aUkJZ6/jULrq/gE=
		</data>
		<key>Resources/_tcl_data/msgs/el.msg</key>
		<data>
		T7nbbnhOHSjmMrVe0x+7tJl79XU=
		</data>
		<key>Resources/_tcl_data/msgs/en_au.msg</key>
		<data>
		Uoku3fp03UyAQPnN0ZqVNr/3K24=
		</data>
		<key>Resources/_tcl_data/msgs/en_be.msg</key>
		<data>
		t6a0v7ZTPMM6Cg9QN+VaVZWMTfw=
		</data>
		<key>Resources/_tcl_data/msgs/en_bw.msg</key>
		<data>
		7uw6WjeA26cXAUnHeRgHSOuGG4Y=
		</data>
		<key>Resources/_tcl_data/msgs/en_ca.msg</key>
		<data>
		v9utXAoyOjfV+Rw37Imbkj2lsPU=
		</data>
		<key>Resources/_tcl_data/msgs/en_gb.msg</key>
		<data>
		3tvbLJrKkyw3PDFftsVpHb7es0Y=
		</data>
		<key>Resources/_tcl_data/msgs/en_hk.msg</key>
		<data>
		ZxIsqo7Kgp7AdZoBR8aFGm6R6Gc=
		</data>
		<key>Resources/_tcl_data/msgs/en_ie.msg</key>
		<data>
		+odlD4QOaRZD82149zJuklaD0Kg=
		</data>
		<key>Resources/_tcl_data/msgs/en_in.msg</key>
		<data>
		cDYlk6KwTPllIT8xixDpLigPM40=
		</data>
		<key>Resources/_tcl_data/msgs/en_nz.msg</key>
		<data>
		M4ayWZx8FwoD5O7WjDnqx63QFwg=
		</data>
		<key>Resources/_tcl_data/msgs/en_ph.msg</key>
		<data>
		GXGs+qV1PSkUV33Mnr30PPicHQA=
		</data>
		<key>Resources/_tcl_data/msgs/en_sg.msg</key>
		<data>
		bJBmOWwQcEnYYc0KnJjeh1N4JXE=
		</data>
		<key>Resources/_tcl_data/msgs/en_za.msg</key>
		<data>
		pbhTo52UTbm7GkwLnVWv3vBRVUg=
		</data>
		<key>Resources/_tcl_data/msgs/en_zw.msg</key>
		<data>
		7/grKHQfoW0t/JO1Qh+FbW+QJQk=
		</data>
		<key>Resources/_tcl_data/msgs/eo.msg</key>
		<data>
		oUuaqZnAu9myHmorRKk01oWJdDA=
		</data>
		<key>Resources/_tcl_data/msgs/es.msg</key>
		<data>
		iy0L4b41TWOewzc/4goPJV4xLvY=
		</data>
		<key>Resources/_tcl_data/msgs/es_ar.msg</key>
		<data>
		PFU1NiQaXS6Vo7qQJKq0a7h/utk=
		</data>
		<key>Resources/_tcl_data/msgs/es_bo.msg</key>
		<data>
		hT/8u5oiU7fcK4LCv8OxMlAPep0=
		</data>
		<key>Resources/_tcl_data/msgs/es_cl.msg</key>
		<data>
		j+HRdpbJEM9ZRnWYIz1VJov+DZQ=
		</data>
		<key>Resources/_tcl_data/msgs/es_co.msg</key>
		<data>
		O6OMsDJYyoNON9u04xSdTNqbNTs=
		</data>
		<key>Resources/_tcl_data/msgs/es_cr.msg</key>
		<data>
		RWyQwJwqiRnclI6GFw9SMGLxNds=
		</data>
		<key>Resources/_tcl_data/msgs/es_do.msg</key>
		<data>
		GA6ThYTwpXrAw/heZXS8SCkdgg4=
		</data>
		<key>Resources/_tcl_data/msgs/es_ec.msg</key>
		<data>
		M2VIyNNhscqovfaY4Uiojkf7J6Y=
		</data>
		<key>Resources/_tcl_data/msgs/es_gt.msg</key>
		<data>
		SZ9p5mGztXRyJ7Md5FOcrzVcyqw=
		</data>
		<key>Resources/_tcl_data/msgs/es_hn.msg</key>
		<data>
		Y5qUJ5RTsAKJlUSP0uIhwb3iPO4=
		</data>
		<key>Resources/_tcl_data/msgs/es_mx.msg</key>
		<data>
		DuWjYnfqTnofTG0dnuMtkJGNolw=
		</data>
		<key>Resources/_tcl_data/msgs/es_ni.msg</key>
		<data>
		UknjFhGmcOru8QWrStLl8Us1XK4=
		</data>
		<key>Resources/_tcl_data/msgs/es_pa.msg</key>
		<data>
		fxTUb2bYqUpJNwLc3npQwdcXdLI=
		</data>
		<key>Resources/_tcl_data/msgs/es_pe.msg</key>
		<data>
		dcKDIa/tPZzaPr8/0FnN6ll7sTo=
		</data>
		<key>Resources/_tcl_data/msgs/es_pr.msg</key>
		<data>
		JNiwlt2PHPoQHW82YG0APU/Me00=
		</data>
		<key>Resources/_tcl_data/msgs/es_py.msg</key>
		<data>
		YZkObz45m4cGDlIqvN53qDIBkWc=
		</data>
		<key>Resources/_tcl_data/msgs/es_sv.msg</key>
		<data>
		mr7CLoLBY4ucjhl3YMZuNwKZu5M=
		</data>
		<key>Resources/_tcl_data/msgs/es_uy.msg</key>
		<data>
		04UyyoToD+cMaRCHEeP5p9/VIw8=
		</data>
		<key>Resources/_tcl_data/msgs/es_ve.msg</key>
		<data>
		fCrygMkLAQSrSbKlJ2AjdCVCdM4=
		</data>
		<key>Resources/_tcl_data/msgs/et.msg</key>
		<data>
		vuOeRfo6drYxtMLQ+Tf/YEHgkzI=
		</data>
		<key>Resources/_tcl_data/msgs/eu.msg</key>
		<data>
		/frCLMCDmyl5kAGDh2XrSiMv0nk=
		</data>
		<key>Resources/_tcl_data/msgs/eu_es.msg</key>
		<data>
		NQP8uUkCYbqUfonVSUmYzrsVciM=
		</data>
		<key>Resources/_tcl_data/msgs/fa.msg</key>
		<data>
		yyEHQPViCOjmIaRdVF1978rovK8=
		</data>
		<key>Resources/_tcl_data/msgs/fa_in.msg</key>
		<data>
		XkF45ygoB0dr0Nbh8uMg5C+g3nc=
		</data>
		<key>Resources/_tcl_data/msgs/fa_ir.msg</key>
		<data>
		nWQDjAAlOn7tpJIbnF40aQ4YUGE=
		</data>
		<key>Resources/_tcl_data/msgs/fi.msg</key>
		<data>
		sXNwDBdjNr0bEjwqBVpoX3O2DAc=
		</data>
		<key>Resources/_tcl_data/msgs/fo.msg</key>
		<data>
		w4IDntfSro2Wzy6lX6Morpz9L30=
		</data>
		<key>Resources/_tcl_data/msgs/fo_fo.msg</key>
		<data>
		5hBevNxUf+Li/p7dycVzu9rYWtA=
		</data>
		<key>Resources/_tcl_data/msgs/fr.msg</key>
		<data>
		G2ie3Cn4vEUXk25dd6CECD8SrjE=
		</data>
		<key>Resources/_tcl_data/msgs/fr_be.msg</key>
		<data>
		j82wHQcp6fGgysVvee23mjdzSvU=
		</data>
		<key>Resources/_tcl_data/msgs/fr_ca.msg</key>
		<data>
		MUW7VNnh5NkWYYbVtD9BHOAlBZQ=
		</data>
		<key>Resources/_tcl_data/msgs/fr_ch.msg</key>
		<data>
		yu19QzS62L5Yahru4nD7aROgNRI=
		</data>
		<key>Resources/_tcl_data/msgs/ga.msg</key>
		<data>
		wqYT3Hw2eoQdmd4Vh29eeoAnu/g=
		</data>
		<key>Resources/_tcl_data/msgs/ga_ie.msg</key>
		<data>
		ru3MIXe1kqACWh28/8DvNjTb9WI=
		</data>
		<key>Resources/_tcl_data/msgs/gl.msg</key>
		<data>
		g6KEiZeFlW7LAVu7hx5+BKfDZYU=
		</data>
		<key>Resources/_tcl_data/msgs/gl_es.msg</key>
		<data>
		aZnoIUjh0XmcOJvMbGlS1VFPSks=
		</data>
		<key>Resources/_tcl_data/msgs/gv.msg</key>
		<data>
		LQQR2i9uBEGxqGg2hxeOnrVSuDU=
		</data>
		<key>Resources/_tcl_data/msgs/gv_gb.msg</key>
		<data>
		TQ7WZoqZusmyc7D6i8dOtrud38g=
		</data>
		<key>Resources/_tcl_data/msgs/he.msg</key>
		<data>
		QIVOuB7mcAhtDQwMLw+dhAbfa0c=
		</data>
		<key>Resources/_tcl_data/msgs/hi.msg</key>
		<data>
		gU+VBvzYtZLCKkcCPnNFfEabL1M=
		</data>
		<key>Resources/_tcl_data/msgs/hi_in.msg</key>
		<data>
		Iu7HT8ARBjBxpAw4YK6O842JhYI=
		</data>
		<key>Resources/_tcl_data/msgs/hr.msg</key>
		<data>
		XgBtGsp7vaybimXvsm+vwDxun94=
		</data>
		<key>Resources/_tcl_data/msgs/hu.msg</key>
		<data>
		xiKyHA26g/lD+9EMdG5fq+ICNbI=
		</data>
		<key>Resources/_tcl_data/msgs/id.msg</key>
		<data>
		aTrMKglyFWuYQQav0HkRrxTE8Zw=
		</data>
		<key>Resources/_tcl_data/msgs/id_id.msg</key>
		<data>
		GP0BeAUVgcnwGWBEmb+RsWcSzJE=
		</data>
		<key>Resources/_tcl_data/msgs/is.msg</key>
		<data>
		BMsZdoRqeOqVk8s3BsnWEXPOAww=
		</data>
		<key>Resources/_tcl_data/msgs/it.msg</key>
		<data>
		RwmGctM5YkR06IVOsFEtVKDKSec=
		</data>
		<key>Resources/_tcl_data/msgs/it_ch.msg</key>
		<data>
		fGiMhpPHau4H+zJjfNWOR6hXYPM=
		</data>
		<key>Resources/_tcl_data/msgs/ja.msg</key>
		<data>
		gVom3CS/FnssKnS1awfBwoQm59Q=
		</data>
		<key>Resources/_tcl_data/msgs/kl.msg</key>
		<data>
		2RKgrroIvJfYDpt6Vc4UaVbJC8w=
		</data>
		<key>Resources/_tcl_data/msgs/kl_gl.msg</key>
		<data>
		6XqUj/5sjemfkZhxVd8KgaYwlQ4=
		</data>
		<key>Resources/_tcl_data/msgs/ko.msg</key>
		<data>
		oYpzYXg4lsaRvVvos6H8zMsBX0M=
		</data>
		<key>Resources/_tcl_data/msgs/ko_kr.msg</key>
		<data>
		WS+P+fq7x79IU5r3SNz8kkGu2C0=
		</data>
		<key>Resources/_tcl_data/msgs/kok.msg</key>
		<data>
		YgW9IzaFfzaMq/iWR/VNlOCTp3s=
		</data>
		<key>Resources/_tcl_data/msgs/kok_in.msg</key>
		<data>
		cA5LnDlbVAv86avcgea5t1iJPck=
		</data>
		<key>Resources/_tcl_data/msgs/kw.msg</key>
		<data>
		nC76YybGKWLc2Duo0W2JYW0sW3c=
		</data>
		<key>Resources/_tcl_data/msgs/kw_gb.msg</key>
		<data>
		emvNa+X0H4S2AN81XLAOy5tK6MA=
		</data>
		<key>Resources/_tcl_data/msgs/lt.msg</key>
		<data>
		WC6yJMlxXIM2tNH8593sDYn1rXE=
		</data>
		<key>Resources/_tcl_data/msgs/lv.msg</key>
		<data>
		lz30DQRkvOEOtZkYBtmZC2WrD4I=
		</data>
		<key>Resources/_tcl_data/msgs/mk.msg</key>
		<data>
		2VPdEj1UsCuvSxrg02CBzfyjhEQ=
		</data>
		<key>Resources/_tcl_data/msgs/mr.msg</key>
		<data>
		52CxQ6hUg44Y/7ZlAPTTEt2AY04=
		</data>
		<key>Resources/_tcl_data/msgs/mr_in.msg</key>
		<data>
		/BemdCv4foG71NXLe03O0NTdZXs=
		</data>
		<key>Resources/_tcl_data/msgs/ms.msg</key>
		<data>
		NPvpn7JaDcov2iwAisgSe6K8Jzs=
		</data>
		<key>Resources/_tcl_data/msgs/ms_my.msg</key>
		<data>
		b/ybFqBgDZvEVzIvExa8F1MJxso=
		</data>
		<key>Resources/_tcl_data/msgs/mt.msg</key>
		<data>
		PaWswPUlGFQYEOfy/ld1GVXhK9o=
		</data>
		<key>Resources/_tcl_data/msgs/nb.msg</key>
		<data>
		U6KY+78Jri4iOwQXhkQ6PYaIyes=
		</data>
		<key>Resources/_tcl_data/msgs/nl.msg</key>
		<data>
		U1cGPVaZGI5UTSROxK7933YGuSI=
		</data>
		<key>Resources/_tcl_data/msgs/nl_be.msg</key>
		<data>
		EbXpX/TYIudqG5wo7sK8XpXl42I=
		</data>
		<key>Resources/_tcl_data/msgs/nn.msg</key>
		<data>
		o4CGOoMg2rHVotYMIu1ffbXHuvc=
		</data>
		<key>Resources/_tcl_data/msgs/pl.msg</key>
		<data>
		+5elgwllcW53Vjvmt+scag6mv0A=
		</data>
		<key>Resources/_tcl_data/msgs/pt.msg</key>
		<data>
		FA1rwfbO9f0KOQs4QgU79UtUtOI=
		</data>
		<key>Resources/_tcl_data/msgs/pt_br.msg</key>
		<data>
		eNkagOJCaoS8iO6X2ijsDkvo3kU=
		</data>
		<key>Resources/_tcl_data/msgs/ro.msg</key>
		<data>
		xUyL8F6ObCwJAdPIjIndzzWiaSQ=
		</data>
		<key>Resources/_tcl_data/msgs/ru.msg</key>
		<data>
		l9/7HiJM7bVCeEHDtZ+FN2zUQjs=
		</data>
		<key>Resources/_tcl_data/msgs/ru_ua.msg</key>
		<data>
		My5MyW56Adp/s5nqFHcKXFGFufI=
		</data>
		<key>Resources/_tcl_data/msgs/sh.msg</key>
		<data>
		N80Ud6MxiDjo1ck9WWoj+ZyECfI=
		</data>
		<key>Resources/_tcl_data/msgs/sk.msg</key>
		<data>
		EEQUXBcU/UTQCLE6Mbx3jfvkeVA=
		</data>
		<key>Resources/_tcl_data/msgs/sl.msg</key>
		<data>
		vmlA7J9MXiKPBD+dRqQiNKAvSgM=
		</data>
		<key>Resources/_tcl_data/msgs/sq.msg</key>
		<data>
		RK7wH1aCUIUQmbqopTb7us0967s=
		</data>
		<key>Resources/_tcl_data/msgs/sr.msg</key>
		<data>
		oUJzMFK4fKUQuJRSVjmc6fhzeUw=
		</data>
		<key>Resources/_tcl_data/msgs/sv.msg</key>
		<data>
		2cO7SuvZv9lCWTaU55aowvuSF7g=
		</data>
		<key>Resources/_tcl_data/msgs/sw.msg</key>
		<data>
		mTWzb/Kxxt/ePsN1vEcaDpPR9+M=
		</data>
		<key>Resources/_tcl_data/msgs/ta.msg</key>
		<data>
		EhZlCBr8M9289nnXR5vwvEf+9xY=
		</data>
		<key>Resources/_tcl_data/msgs/ta_in.msg</key>
		<data>
		lN7wBWx+MILlgma85DamHAReo5Q=
		</data>
		<key>Resources/_tcl_data/msgs/te.msg</key>
		<data>
		9DoPbMvdvdXqFAx/pV6agquRCgM=
		</data>
		<key>Resources/_tcl_data/msgs/te_in.msg</key>
		<data>
		0yPAN0f+aOm3P35cHhCxaKQPKi8=
		</data>
		<key>Resources/_tcl_data/msgs/th.msg</key>
		<data>
		r9l/jozBTTBt7dePjzlXOOOKhWk=
		</data>
		<key>Resources/_tcl_data/msgs/tr.msg</key>
		<data>
		TuPi34ZhLbMU+NPnIU174kGqGjI=
		</data>
		<key>Resources/_tcl_data/msgs/uk.msg</key>
		<data>
		ziYYeHXjNMcS/atz5rUmJHxv4c8=
		</data>
		<key>Resources/_tcl_data/msgs/vi.msg</key>
		<data>
		s+XcCVlz5G2ICDJrKh/EUEa1Jn8=
		</data>
		<key>Resources/_tcl_data/msgs/zh.msg</key>
		<data>
		qH8g96Mx3vwzSW7NpQ2FXIOW4EA=
		</data>
		<key>Resources/_tcl_data/msgs/zh_cn.msg</key>
		<data>
		sFUxCL3kOqftNi4r/68avKFWdJE=
		</data>
		<key>Resources/_tcl_data/msgs/zh_hk.msg</key>
		<data>
		+9YJV25ltW7aZ/2KGAGie0PbVIY=
		</data>
		<key>Resources/_tcl_data/msgs/zh_sg.msg</key>
		<data>
		Ix/xtvhZ0CYfFdJCLfCedWzlDMs=
		</data>
		<key>Resources/_tcl_data/msgs/zh_tw.msg</key>
		<data>
		rx7lNqq7gZi6iNNHTtSfdqN+if8=
		</data>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<data>
		8H+g2HJ3MJuHCq6xxdK3iBtALlQ=
		</data>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<data>
		AxQPneIK2LCHnn/IcEc+htq8V+w=
		</data>
		<key>Resources/_tcl_data/package.tcl</key>
		<data>
		vJDySk9RRrXxu1qivqTuvC0EwKc=
		</data>
		<key>Resources/_tcl_data/parray.tcl</key>
		<data>
		oLG9Tmjc4XaNPF4NPHsx4oAh07o=
		</data>
		<key>Resources/_tcl_data/safe.tcl</key>
		<data>
		W2OxumZBUUfHmOwwGAeUeI0iZ+4=
		</data>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<data>
		km85h6X//lSHequmKjsANnlOM48=
		</data>
		<key>Resources/_tcl_data/tclIndex</key>
		<data>
		vyxjBh2FKaSQnUrAW9O9PB1l4qc=
		</data>
		<key>Resources/_tcl_data/tm.tcl</key>
		<data>
		w5Piy0thL6AFlXwENtMHdvDFf6A=
		</data>
		<key>Resources/_tcl_data/word.tcl</key>
		<data>
		EHWAJskYyJheR9QvBglh+o7+vdk=
		</data>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<data>
		ThycTe1bKIcdTJwWNWggUgaxihw=
		</data>
		<key>Resources/_tk_data/button.tcl</key>
		<data>
		PguQVCjCk/IQdBRf5DKB8i5pnrQ=
		</data>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<data>
		b4+6WX8IUIPK3Y5xk9F2/qCinOc=
		</data>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<data>
		TAQKzEzSEXP49phjV0NARrvQzDc=
		</data>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<data>
		UeK1uukZTnsbvTTK/Ko9IrXhxrc=
		</data>
		<key>Resources/_tk_data/console.tcl</key>
		<data>
		KeLufRHi5BZ6l7kPRP1PGnVQdkI=
		</data>
		<key>Resources/_tk_data/dialog.tcl</key>
		<data>
		wYBuvfc08XAqPlQl+5FaAImE8Hw=
		</data>
		<key>Resources/_tk_data/entry.tcl</key>
		<data>
		UboQkLAvC5oVA8URpH0RFzlOE8E=
		</data>
		<key>Resources/_tk_data/focus.tcl</key>
		<data>
		aDWlFehanlXVonBz2uHxpddCRRM=
		</data>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<data>
		e6wFZyls0Z4ft+nRs67in1XccN4=
		</data>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<data>
		o675ntOsggS/0Q32SWt/Lp3Lsuk=
		</data>
		<key>Resources/_tk_data/icons.tcl</key>
		<data>
		Jq4ArEnwnaDs/x2y8CGYOCO3fxI=
		</data>
		<key>Resources/_tk_data/images/README</key>
		<data>
		8boyJEjSBmI/j+c0GS84PY9/oZg=
		</data>
		<key>Resources/_tk_data/images/logo.eps</key>
		<data>
		K0mbfE68hVTswHuECGMsr0B/ttU=
		</data>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<data>
		vLc9ivJihGOhuVVYGZnHfwn4Bbg=
		</data>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<data>
		6lIhmjehQP2YrqZupUaF3YFY2bE=
		</data>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<data>
		3dEOeYryCe/OAi6XRI5e4RzrViE=
		</data>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<data>
		E0iOTyhnbx4M44P4DRNRDwcZi5k=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<data>
		ilj8GbIL/ciRNRXZsyzL+Kz5I0Q=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<data>
		vZceca6AXCwuUd1UTQBukjY7bAw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<data>
		GhWCZQ4hiwvm/97/1k0n9Lmphw8=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<data>
		jbf7RTt5uPK05nrDCkultb3evTs=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<data>
		+pig/YkQ3y77FO2uwDi045H+qzw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<data>
		5bHe1JCVMyI2Q5U47NndCx/Uk0s=
		</data>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<data>
		JnqVIMQ5AiHc5QF354mk69WQ9IQ=
		</data>
		<key>Resources/_tk_data/listbox.tcl</key>
		<data>
		qiZADGyG6c17jazOTKuAt64hqXg=
		</data>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<data>
		n4k0mIglWV9zEc/w0OGn+45GNKQ=
		</data>
		<key>Resources/_tk_data/menu.tcl</key>
		<data>
		Ltd65Ojk6Jd8RhBBQDplHK9/c3A=
		</data>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<data>
		kl2XO3AlI4TR3ps4jGwgOOZG/d8=
		</data>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<data>
		RsGIL5eKTXpu0NLyIO3L2J27+z8=
		</data>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<data>
		lebHHkUlqN2R5Ii5UmZa6cX73e0=
		</data>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<data>
		UprgsMudHbx/iETzRhSeFR3go2s=
		</data>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<data>
		y0VfkQII4uVbJ6lqvYRf7tqIcRo=
		</data>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<data>
		JVMd9iYuOxFwBVc1xah0uRJP6oM=
		</data>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<data>
		V1QprquvZkBCWsG8OXszgsHtESI=
		</data>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<data>
		bC1rYiQpq4wX4HwuD1RkaYI6vlc=
		</data>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<data>
		pY/pWpYS3lzav/XdzuABmitlFgE=
		</data>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<data>
		jDUW95+3LzKEi0AJHaZ8geQP3v4=
		</data>
		<key>Resources/_tk_data/msgs/fi.msg</key>
		<data>
		fepoJVjo3Oc47h97QpfMX5e180I=
		</data>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<data>
		ytOAWQDoYLlJHj7lwsD1KtymcGU=
		</data>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<data>
		GhSNIwyfjXSNlqec1OJhryZNZSQ=
		</data>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<data>
		X7FjvBCG0zZiKCBAePIZ/ku2fLM=
		</data>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<data>
		j2r/aLQrdH0whw1tp+BYKUkhQGo=
		</data>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<data>
		PkXAECsoeQjXcKMdGQZnjnhQiMI=
		</data>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<data>
		lI7pX0VJ2ox9QSkR0XtLYsuiKt0=
		</data>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<data>
		FpDFn6nzZwDY86ptWYUAjuEls/Q=
		</data>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<data>
		KNnbnL7nkcCb0nLZwqbD2oDrieo=
		</data>
		<key>Resources/_tk_data/msgs/zh_cn.msg</key>
		<data>
		CRYOLe02340zZ4hksZ+HFZjuKSA=
		</data>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<data>
		MOJFld1oPkcP6fEoFNJ9bSZrUR4=
		</data>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<data>
		l0XIPuyFZWAvjXRhBCSEgAn/pnA=
		</data>
		<key>Resources/_tk_data/palette.tcl</key>
		<data>
		oyLM+zP/c+SkcwtbId5CkPnZRiI=
		</data>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<data>
		3+PcZjwZ6aUFJqUTBD0jk4adj5A=
		</data>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<data>
		ZSiHGCo9AoELSub39lieo1AapaQ=
		</data>
		<key>Resources/_tk_data/safetk.tcl</key>
		<data>
		0ZOw5gErQuu06VsONbGpzawlIhw=
		</data>
		<key>Resources/_tk_data/scale.tcl</key>
		<data>
		PlppetqYRK09qYBe2RIx0nCM88E=
		</data>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<data>
		I+b3CVBm7TtlmYMkAh1mXYEOapM=
		</data>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<data>
		mxZmqeyYka/MzK+9XM/L/pBOhdQ=
		</data>
		<key>Resources/_tk_data/tclIndex</key>
		<data>
		Cco5ZHqhwU2xYBQFXkipsCN2Obo=
		</data>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<data>
		qKEoPyejUVzAzyijvYfWV79Vnh8=
		</data>
		<key>Resources/_tk_data/text.tcl</key>
		<data>
		TLr1vX72+ATQqphoF0bhQ/ytO2I=
		</data>
		<key>Resources/_tk_data/tk.tcl</key>
		<data>
		kLiJyKO4OvRPd6VzKDhChPLESOU=
		</data>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<data>
		8JMX4S2O4t0KVuZHQ2za78mjZAQ=
		</data>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<data>
		LQrPWwmF+yYZY9yq4DNHdjQ44xE=
		</data>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<data>
		KgnTJcpWyTCzr7HuQ8lE/UQWuOE=
		</data>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<data>
		eEpCBadYjDG8ovZ/KNcMD5TBWg4=
		</data>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<data>
		AXomewLff3oqIW8UuGgM/wQhQBE=
		</data>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<data>
		fTpNk8JdxUhM47CObct6RI5VRFM=
		</data>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<data>
		Sx1j1Zxyw1eTGoy78HFlRJKps3E=
		</data>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<data>
		1T76HcSytlMfTKQc5YVcF74LBS4=
		</data>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<data>
		DddlHcbhwfX1FmoNlY7+BUW+gJ0=
		</data>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<data>
		zKBkgeHR45vHiP29zywUAmGb5A4=
		</data>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<data>
		mV33YkSBrPKdFCtlrr5NUjIuYl0=
		</data>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<data>
		Snv5G+hfEN/He3uOA7N+NO5R7q8=
		</data>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<data>
		lcLabSCzCUXS2BZhbPyTH0iOn/c=
		</data>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<data>
		rwHBo5Y9koMB1rwslNDYfd3uA0Q=
		</data>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<data>
		yzz5l21pBDYgsr/84ZXlt9omFdo=
		</data>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<data>
		0HCgHMWnhySbxtrRhLJJxN03OWo=
		</data>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<data>
		g2QnCoeAp1uLLC1RceoH+ianNls=
		</data>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<data>
		AYqBGx/LdQDDrWehv52PGJlLtM0=
		</data>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<data>
		6+kb5uR8t/pMLMxxBynxPVCObVU=
		</data>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<data>
		9qZnarr+F4GNITEpohU2bzkLgQs=
		</data>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<data>
		I9LaoEaT61JA5Ax0PL/wYUt8Bxk=
		</data>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<data>
		v6wUwzmcnylWfLYjQ3JuvNTOx9s=
		</data>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<data>
		8h3SJJPPQZU72Fu8cAB36bRTT+A=
		</data>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<data>
		ZBxEfzDfISM6N1S/zS5c7PHmKo0=
		</data>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<data>
		25gQh/KjETYURgFPrb2LGZ2FZxY=
		</data>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<data>
		6GtMIoi6Dn+JJSka7YAn8DTbgnM=
		</data>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<data>
		gIL95QxCjSURsF9Sn8zwJlHVrJM=
		</data>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<data>
		GBqdjL2eeFxpGUmsJTamHaS0USo=
		</data>
		<key>Resources/base_library.zip</key>
		<data>
		R8BfGgeCdKLn4pr5IvP6s5nIEGM=
		</data>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<data>
		INBhs7dCz6MeX7yGLTT1V1NO/b8=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<data>
		/dyLHGiO87rtDVpGq/XwHw7a8Cs=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<data>
		hNECSIc4sOu8elCHlz7/vVTJW9U=
		</data>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<data>
		s2ExZOWH0JwFLDTM3E1E2sT/ROI=
		</data>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<data>
		Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
		</data>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<data>
		+evgfnnhRvedyIp/+JQsDkMEnw0=
		</data>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<data>
		TwB32rbJhqZKuTkmMAJMsJdyseg=
		</data>
		<key>Resources/img/123.webp</key>
		<data>
		tU9ln24m/WldMzrodXCCV+zrzeE=
		</data>
		<key>Resources/img/bluetooth.icns</key>
		<data>
		BWQueLjvWPlbIq/Kh/iUbQpap6s=
		</data>
		<key>Resources/img/device.icns</key>
		<data>
		sV33xFn17BTEvfFLUBfa2Yv1sxY=
		</data>
		<key>Resources/img/erase.png</key>
		<data>
		gBENmg8aOCjcY+1IYvKYPIcz/z0=
		</data>
		<key>Resources/img/exit.png</key>
		<data>
		VWk+nE0rFweKNEjgXsZAgVd9rHI=
		</data>
		<key>Resources/img/findmy.icns</key>
		<data>
		EzmzWjuyERvTr2fQDimaOfU3Iqw=
		</data>
		<key>Resources/img/shutdown.png</key>
		<data>
		K0UjnvC4DqC907tryPgh+wfMy/0=
		</data>
		<key>Resources/img/sysinfo.icns</key>
		<data>
		Ltqqxr6FZCqK7Arl2g1zyZe3mCQ=
		</data>
		<key>Resources/img/vs_ade_checker.png</key>
		<data>
		Acz5YwpStVTXr8Jtfzn5pQKdKg4=
		</data>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		8SU9TvCuDu5FePRUdqXMkButmQA=
		</data>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<data>
		ldkboZcNMcgtxMqVzFijk+UAV4s=
		</data>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<data>
		JFvir4nfiG5ijDbsAcYQx5/aljU=
		</data>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<data>
		OwGsAniYvxO5Qk3uKKBarLJ1XzU=
		</data>
		<key>Resources/img/vs_icns/vs1.png</key>
		<data>
		WTy3qC0l5ptb5QEORcAlIreVZG8=
		</data>
		<key>Resources/img/vs_icns/vs2.png</key>
		<data>
		fN+Ic+xtw43hAA2OYq4XMWw60oQ=
		</data>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<data>
		XWVuYXsulSYsBi6hS/M9yWJxTwY=
		</data>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<data>
		Csp0Pn/Bo31hLGWXKSXAnj4PqR0=
		</data>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<data>
		rBNRUBIJKhuwT3L4ycOkP9De1Ew=
		</data>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<data>
		3w08zKeFThN4uhnlb/nlhXnxFuo=
		</data>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<data>
		e29Oba/lrQE1Wezk7M++DzQGbhc=
		</data>
		<key>Resources/img/vs_mac_tool_v2.icns</key>
		<data>
		6DLidyRNkn4oTIyC6LI0fS/sDMM=
		</data>
		<key>Resources/img/vs_mac_tool_v2.png</key>
		<data>
		tm8iNmkmZ8VXl/C4cczT2Mh3RD0=
		</data>
		<key>Resources/img/vs_mac_tool_v2_rounded.png</key>
		<data>
		wAMt3GxURS5hENvaxquEIyxAjdE=
		</data>
		<key>Resources/img/vsmactool.png</key>
		<data>
		gssccyBWyzZKOgzo4SDNHdgnYgo=
		</data>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<data>
		oBD4j/438pcF8UfpEu8/M3JP/X8=
		</data>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<data>
		DgyO1FNbP2AQLgHJdfq3BLYh2Vo=
		</data>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<data>
		UqlH76EyJ99OUmMBapgim5ct8fU=
		</data>
		<key>Resources/img/xxx/bluetooth.png</key>
		<data>
		9ldniYDf5s5W2rBtESN4G9Q15II=
		</data>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<data>
		boiqyT6aJvpi4JNeYXWVP0JHLNQ=
		</data>
		<key>Resources/img/xxx/checkADE.icns</key>
		<data>
		KBz6o1IJg9OplEnKhcHyPj/T4Vw=
		</data>
		<key>Resources/img/xxx/checkADE.png</key>
		<data>
		lQIica3CIebx69ddnBk4tn9b06k=
		</data>
		<key>Resources/img/xxx/checkADE2.png</key>
		<data>
		eeCm4B12OpiZAoTfNHGtmY7pAro=
		</data>
		<key>Resources/img/xxx/clear.png</key>
		<data>
		ALKnMwBx3+0JyClpaiw6/LZKT/Q=
		</data>
		<key>Resources/img/xxx/device.icns</key>
		<data>
		gFaGbZ06QBxXraWQvzdyyDGGj0E=
		</data>
		<key>Resources/img/xxx/panda.icns</key>
		<data>
		IbLGCBrdrS+78zbZMTa9U0dm6CE=
		</data>
		<key>Resources/img/xxx/panda.ico</key>
		<data>
		QFalV5j9coD4nKk4mAHrAGFDAM4=
		</data>
		<key>Resources/img/xxx/xxx.png</key>
		<data>
		QHrz8+tr6hEUo/qttHsBz6W8/pw=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<data>
		BEXtD2mRDuruA28Jo5oTxuHzfhI=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<data>
		WRVfQD62iIV68exEpB8/CT8tAzw=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<data>
		cpu/cCDMMCsh0LtImKmiQmUN3h8=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<data>
		8dH5Vh360Fue0WnPyApHzleku2o=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<data>
		iRVBKy2hRNmOZD53OXV7DrKY+sc=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<data>
		+Bx3AhWqVcspD92h48yte3axJNs=
		</data>
		<key>Resources/password_dialog.py</key>
		<data>
		FQw4dffVqjHgTXzPT+LYi4qdmyY=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<data>
		K4uBUimqimHkg/tLoFiLi2xJGJA=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<data>
		6Mgw2LCUIwDHyHs7j9FeoTluB70=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<data>
		s5THrsFYNQuvZ2rjGXvvTXFYsxw=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<data>
		eCm0Mku1QnmUlBMaJw7Dva1N7e8=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<data>
		TmH5Jk3nR4O1kkJJvP4bBvF4ua0=
		</data>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<data>
		eDY2ciZNnNP3LVwdNmXhZXsaUHE=
		</data>
		<key>Resources/tcl8/8.4/platform-1.0.19.tm</key>
		<data>
		YdK+5LeSYCeDUJi5lbT1zNrO1NU=
		</data>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<data>
		T2Zyotbpbm+05Mbi6TMz8oeQGCc=
		</data>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<data>
		VfzjObpSzVD3gAKpksjOmr7LN14=
		</data>
		<key>Resources/tcl8/8.5/tcltest-2.5.7.tm</key>
		<data>
		sCcCDVqykh4hx54Qw+6wO7R1UP8=
		</data>
		<key>Resources/tcl8/8.6/http-2.9.8.tm</key>
		<data>
		+WWXcftPnBHnueYmxi8ROpKfxZc=
		</data>
		<key>Resources/tcl8/8.6/tdbc/sqlite3-1.1.7.tm</key>
		<data>
		SL65b46EfuzbSnA5kE88MOiLYtk=
		</data>
		<key>Resources/vs_mac_tool_v2.icns</key>
		<data>
		6DLidyRNkn4oTIyC6LI0fS/sDMM=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libXau.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Si4OnRngRTa+jTRUlZzqBmpzqtA=
			</data>
			<key>requirement</key>
			<string>cdhash H"4a2e0e9d19e04536be8d3454959cea066a73aad0"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			UJis91UF7Jrf3ZTT7wAtTkvhslg=
			</data>
			<key>requirement</key>
			<string>cdhash H"5098acf75505ec9adfdd94d3ef002d4e4be1b258"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			tJa3uYtQVlfmFCKgHBSL/LEoS2o=
			</data>
			<key>requirement</key>
			<string>cdhash H"b496b7b98b505657e61422a01c148bfcb1284b6a"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			TMqB03NQ2ysyhDdmhvfARPCDSjE=
			</data>
			<key>requirement</key>
			<string>cdhash H"4cca81d37350db2b3284376686f7c044f0834a31"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			E+cQXPrHTwpOGgmjdzlWFdaOSrY=
			</data>
			<key>requirement</key>
			<string>cdhash H"13e7105cfac74f0a4e1a09a377395615d68e4ab6"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			zbN8Q+gE9QM8OL99wrtdBKSxPu0=
			</data>
			<key>requirement</key>
			<string>cdhash H"cdb37c43e804f5033c38bf7dc2bb5d04a4b13eed"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libtiff.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			U3mgo79HxY8QZB4pQ3mbuAdGw28=
			</data>
			<key>requirement</key>
			<string>cdhash H"5379a0a3bf47c58f10641e2943799bb80746c36f"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebp.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Cr5WtiWUEmiyPRpOK5DPg6VYzEM=
			</data>
			<key>requirement</key>
			<string>cdhash H"0abe56b625941268b23d1a4e2b90cf83a558cc43"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			dZxCmeSwHeAWAW6xTAzEHHC175g=
			</data>
			<key>requirement</key>
			<string>cdhash H"759c4299e4b01de016016eb14c0cc41c70b5ef98"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			+UyonNgB5OAMawYO2rxRPdTBIHw=
			</data>
			<key>requirement</key>
			<string>cdhash H"f94ca89cd801e4e00c6b060edabc513dd4c1207c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lBL5Yj9tGAOvKFe6ZbQi4sAdNh8=
			</data>
			<key>requirement</key>
			<string>cdhash H"9412f9623f6d1803af2857ba65b422e2c01d361f"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Pq3IYFHg84MNzxlTdUe5m/iAcvI=
			</data>
			<key>requirement</key>
			<string>cdhash H"3eadc86051e0f3830dcf19537547b99bf88072f2"</string>
		</dict>
		<key>Frameworks/PIL/_imaging.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			xDToFH18Qx1kBLhxDlytEhatqs0=
			</data>
			<key>requirement</key>
			<string>cdhash H"c434e8147d7c431d6404b8710e5cad1216adaacd"</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OIHzMB4ODfXp3qojgZGoISAh1t0=
			</data>
			<key>requirement</key>
			<string>cdhash H"3881f3301e0e0df5e9deaa238191a8212021d6dd"</string>
		</dict>
		<key>Frameworks/PIL/_imagingmath.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OeUWu/kGTbx53OI6VFLqs7rNZUg=
			</data>
			<key>requirement</key>
			<string>cdhash H"39e516bbf9064dbc79dce23a5452eab3bacd6548"</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fZ4xwVyWZCWKe5+05FST7cAThr4=
			</data>
			<key>requirement</key>
			<string>cdhash H"7d9e31c15c9664258a7b9fb4e45493edc01386be"</string>
		</dict>
		<key>Frameworks/PIL/_webp.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			7ZKE3Jv9J6VdrA2K6lHFm6QeHNQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"ed9284dc9bfd27a55dac0d8aea51c59ba41e1cd4"</string>
		</dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.12/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			nTQcC4oJdBqNjzd8Ok18ihrDVJI=
			</data>
			<key>requirement</key>
			<string>cdhash H"9d341c0b8a09741a8d8f377c3a4d7c8a1ac35492"</string>
		</dict>
		<key>Frameworks/_tcl_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tcl_data</string>
		</dict>
		<key>Frameworks/_tk_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tk_data</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/customtkinter</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/customtkinter</string>
		</dict>
		<key>Frameworks/img</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/img</string>
		</dict>
		<key>Frameworks/keyring-25.6.0.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/keyring-25.6.0.dist-info</string>
		</dict>
		<key>Frameworks/lib-dynload/_asyncio.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DQ2TwKVRsu1q/PakZ8c9QjUC0Rc=
			</data>
			<key>requirement</key>
			<string>cdhash H"0d0d93c0a551b2ed6afcf6a467c73d423502d117"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dRwv5rUIHKBPwdAQIvJ8Fr9rnb0=
			</data>
			<key>requirement</key>
			<string>cdhash H"751c2fe6b5081ca04fc1d01022f27c16bf6b9dbd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			k7E4ICXsJQ9vd1CAO//HcQJyWbo=
			</data>
			<key>requirement</key>
			<string>cdhash H"93b1382025ec250f6f7750803bffc771027259ba"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			cHXhGsC/M/GYc/r5iIIlG1kNN5Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"7075e11ac0bf33f19873faf98882251b590d3794"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			68sf88zPC2ie28nvFIIvqcltjZE=
			</data>
			<key>requirement</key>
			<string>cdhash H"ebcb1ff3cccf0b689edbc9ef14822fa9c96d8d91"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			xxCr1MTa1UJ2TMH5eXNYnEjcdHg=
			</data>
			<key>requirement</key>
			<string>cdhash H"c710abd4c4dad542764cc1f97973589c48dc7478"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/xnx+ZriXPoOGGK0eU+pkh4zW6k=
			</data>
			<key>requirement</key>
			<string>cdhash H"ff19f1f99ae25cfa0e1862b4794fa9921e335ba9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Khzd+msyq4ixNUdlDhWqkjMRhng=
			</data>
			<key>requirement</key>
			<string>cdhash H"2a1cddfa6b32ab88b13547650e15aa9233118678"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BAHfiuknk2K8qkvIcNBVED0SAPo=
			</data>
			<key>requirement</key>
			<string>cdhash H"0401df8ae9279362bcaa4bc870d055103d1200fa"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YDuhTYmUwSp4YDHrY5xSooFzGs4=
			</data>
			<key>requirement</key>
			<string>cdhash H"603ba14d8994c12a786031eb639c52a281731ace"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			f14KVbSmfzKfbCVOM0MIBIsTQtw=
			</data>
			<key>requirement</key>
			<string>cdhash H"7f5e0a55b4a67f329f6c254e334308048b1342dc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			RPiST49P9E7kHdiKS1x+gukSHZY=
			</data>
			<key>requirement</key>
			<string>cdhash H"44f8924f8f4ff44ee41dd88a4b5c7e82e9121d96"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ctypes.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			RPgwzSkS2LNoMzqkCRwf9QOqMmw=
			</data>
			<key>requirement</key>
			<string>cdhash H"44f830cd2912d8b368333aa4091c1ff503aa326c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/RiZMEXz+qGDQAsFshYeodyCcTg=
			</data>
			<key>requirement</key>
			<string>cdhash H"fd18993045f3faa183400b05b2161ea1dc827138"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wZCQ22rlLbj44hPDQhYViz8mOJY=
			</data>
			<key>requirement</key>
			<string>cdhash H"c19090db6ae52db8f8e213c34216158b3f263896"</string>
		</dict>
		<key>Frameworks/lib-dynload/_elementtree.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fPKIyAs6sVsS8pej2A5Lt7q0zkA=
			</data>
			<key>requirement</key>
			<string>cdhash H"7cf288c80b3ab15b12f297a3d80e4bb7bab4ce40"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kQLQ4eLfrnrFuObbPIKuoNvdKds=
			</data>
			<key>requirement</key>
			<string>cdhash H"9102d0e1e2dfae7ac5b8e6db3c82aea0dbdd29db"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yDLfaLAnY2vXSjQRlfV+9rxpqNw=
			</data>
			<key>requirement</key>
			<string>cdhash H"c832df68b027636bd74a341195f57ef6bc69a8dc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XuO54ehyBmmdbSsk8TgGd5+tvFA=
			</data>
			<key>requirement</key>
			<string>cdhash H"5ee3b9e1e87206699d6d2b24f13806779fadbc50"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			cdqpRw79NFMLcdWOi2VZ2gbqxPg=
			</data>
			<key>requirement</key>
			<string>cdhash H"71daa9470efd34530b71d58e8b6559da06eac4f8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			cXnu3GlTVZtSpcDFKB5Aefuls/c=
			</data>
			<key>requirement</key>
			<string>cdhash H"7179eedc6953559b52a5c0c5281e4079fba5b3f7"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			g5n7B5RXUPFc6k9pk8iThyQcDWo=
			</data>
			<key>requirement</key>
			<string>cdhash H"8399fb07945750f15cea4f6993c89387241c0d6a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multiprocessing.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			lYv4eVd1vpKQDebhr9xOC+GrlYw=
			</data>
			<key>requirement</key>
			<string>cdhash H"958bf8795775be92900de6e1afdc4e0be1ab958c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			X4E6lPyW3gXHuI92tp0j2Cwzgyc=
			</data>
			<key>requirement</key>
			<string>cdhash H"5f813a94fc96de05c7b88f76b69d23d82c338327"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hlU3h1M4H6+y4T8WXMrt6xlcpTo=
			</data>
			<key>requirement</key>
			<string>cdhash H"8655378753381fafb2e13f165ccaedeb195ca53a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixshmem.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Wr2tyBZAeRWdmbR6Yb30xYjT5R4=
			</data>
			<key>requirement</key>
			<string>cdhash H"5abdadc8164079159d99b47a61bdf4c588d3e51e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			4rc7XYTouPzsc80++X3eQYpjKnw=
			</data>
			<key>requirement</key>
			<string>cdhash H"e2b73b5d84e8b8fcec73cd3ef97dde418a632a7c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BpTWMZAfhC+wxLt4LFUVeJO5jB8=
			</data>
			<key>requirement</key>
			<string>cdhash H"0694d631901f842fb0c4bb782c55157893b98c1f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			2uiHRM+cHh71HaHjRx1aHFgdAXc=
			</data>
			<key>requirement</key>
			<string>cdhash H"dae88744cf9c1e1ef51da1e3471d5a1c581d0177"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dKkKuGhCpDgJbobTsgxAmxX2vb8=
			</data>
			<key>requirement</key>
			<string>cdhash H"74a90ab86842a438096e86d3b20c409b15f6bdbf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+j4gZmSrcq3a5hOHcjxtLHiPIws=
			</data>
			<key>requirement</key>
			<string>cdhash H"fa3e206664ab72addae61387723c6d2c788f230b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha2.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qy48RfJQDDG5QYAoLfxVTw5vROc=
			</data>
			<key>requirement</key>
			<string>cdhash H"ab2e3c45f2500c31b94180282dfc554f0e6f44e7"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5KlgUbwFCi79uWo54HtHmsrkHwo=
			</data>
			<key>requirement</key>
			<string>cdhash H"e4a96051bc050a2efdb96a39e07b479acae41f0a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			8WexxrEPxmneCzAFdK+wprsw8cQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"f167b1c6b10fc669de0b300574afb0a6bb30f1c4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			k9ez8mWLGqnEtMh8KTey9reMxKY=
			</data>
			<key>requirement</key>
			<string>cdhash H"93d7b3f2658b1aa9c4b4c87c2937b2f6b78cc4a6"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			n0eSbkjBKWScWMCKpvXnJAHBoP0=
			</data>
			<key>requirement</key>
			<string>cdhash H"9f47926e48c129649c58c08aa6f5e72401c1a0fd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rzyoV7FfWninHQMv6v9l82Vngd4=
			</data>
			<key>requirement</key>
			<string>cdhash H"af3ca857b15f5a78a71d032feaff65f3656781de"</string>
		</dict>
		<key>Frameworks/lib-dynload/_tkinter.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MEesV4qxYkqaFu+mqYUva/C/ofU=
			</data>
			<key>requirement</key>
			<string>cdhash H"3047ac578ab1624a9a16efa6a9852f6bf0bfa1f5"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DKhYFKcHADAyKrDp/VvGK8FelU0=
			</data>
			<key>requirement</key>
			<string>cdhash H"0ca85814a7070030322ab0e9fd5bc62bc15e954d"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MaJo9a9AS/swsBrmSgt7guWkNYo=
			</data>
			<key>requirement</key>
			<string>cdhash H"31a268f5af404bfb30b01ae64a0b7b82e5a4358a"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+ZxTNGY66zELYGT2RATCAa2tqQ8=
			</data>
			<key>requirement</key>
			<string>cdhash H"f99c5334663aeb310b6064f64404c201adada90f"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ha57+mXkZ7PVXlrn/Ln8txYz9Kc=
			</data>
			<key>requirement</key>
			<string>cdhash H"85ae7bfa65e467b3d55e5ae7fcb9fcb71633f4a7"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Peg2iqDS4cqS+t4XJ27/4yyMhdA=
			</data>
			<key>requirement</key>
			<string>cdhash H"3de8368aa0d2e1ca92fade17276effe32c8c85d0"</string>
		</dict>
		<key>Frameworks/lib-dynload/mmap.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yCf8quwan46qOGVfaeKu8VF3pIM=
			</data>
			<key>requirement</key>
			<string>cdhash H"c827fcaaec1a9f8eaa38655f69e2aef15177a483"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rJQhBeXqsX4eyR5A0CPly826b7Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"ac942105e5eab17e1ec91e40d023e5cbcdba6fb4"</string>
		</dict>
		<key>Frameworks/lib-dynload/readline.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			a7ax/ojJExtVnXkGED5Ab4Wfh/8=
			</data>
			<key>requirement</key>
			<string>cdhash H"6bb6b1fe88c9131b559d7906103e406f859f87ff"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NmD9lnQmeFPw6qMpPTORfOb/IXA=
			</data>
			<key>requirement</key>
			<string>cdhash H"3660fd9674267853f0eaa3293d33917ce6ff2170"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			i7aCjo3bqq6YvJcTicRwSwS2Bt8=
			</data>
			<key>requirement</key>
			<string>cdhash H"8bb6828e8ddbaaae98bc971389c4704b04b606df"</string>
		</dict>
		<key>Frameworks/lib-dynload/syslog.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ghS9lbs9Tm8+lbTL4rFpbuArz/8=
			</data>
			<key>requirement</key>
			<string>cdhash H"8214bd95bb3d4e6f3e95b4cbe2b1696ee02bcfff"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ZiuLAXrP6eFsdCnQwWoocF1Jdzo=
			</data>
			<key>requirement</key>
			<string>cdhash H"662b8b017acfe9e16c7429d0c16a28705d49773a"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			C5xR/FCPdiK9ngVP5ml5/Ql6aL8=
			</data>
			<key>requirement</key>
			<string>cdhash H"0b9c51fc508f7622bd9e054fe66979fd097a68bf"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-312-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ram/8baruPER9iSjYLUDt+9Pl6Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"45a9bff1b6abb8f111f624a360b503b7ef4f97a4"</string>
		</dict>
		<key>Frameworks/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Frameworks/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			maE/zVA7SdWiR3jO1TkhPvhf5/c=
			</data>
			<key>requirement</key>
			<string>cdhash H"99a13fcd503b49d5a24778ced539213ef85fe7f7"</string>
		</dict>
		<key>Frameworks/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Frameworks/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Frameworks/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Frameworks/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Frameworks/libssl.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			gDA6tThqJHx318Zb9Uqagwm3ZTs=
			</data>
			<key>requirement</key>
			<string>cdhash H"80303ab5386a247c77d7c65bf54a9a8309b7653b"</string>
		</dict>
		<key>Frameworks/libtcl8.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oDfSxyvmZC2U47WT2UFed8QlzbA=
			</data>
			<key>requirement</key>
			<string>cdhash H"a037d2c72be6642d94e3b593d9415e77c425cdb0"</string>
		</dict>
		<key>Frameworks/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Frameworks/libtk8.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			uTuAoRI/QOwFjia2fSCUwfoaUY8=
			</data>
			<key>requirement</key>
			<string>cdhash H"b93b80a1123f40ec058e26b67d2094c1fa1a518f"</string>
		</dict>
		<key>Frameworks/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Frameworks/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Frameworks/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Frameworks/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Frameworks/password_dialog.py</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/password_dialog.py</string>
		</dict>
		<key>Frameworks/setuptools</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/setuptools</string>
		</dict>
		<key>Frameworks/tcl8</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/tcl8</string>
		</dict>
		<key>Resources/PIL</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/PIL</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.12/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/_tcl_data/auto.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			IwIjTxI6EP7zFapQiTvTSbxSIB+hL23HvAn9/I6Ev4U=
			</data>
		</dict>
		<key>Resources/_tcl_data/clock.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CNYeNrtTaqAv2gQKvq1VaoYgGO+ae0ZfEGOpc9jaOvA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KUyXF1/QiUCTuGbnNUiuZgru0MPMHnOGfrZuUtNMDdI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Rlri1IgLgAaxR2zWD6z2dodUOCRMHZOn2+TN4QNedF8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			CpX2f5CxzdQHp46kAyr565lvw4hkxG10tCs6f37aDIo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			G0Lffn1rD+sXywvI2X5s5omUkjBt2IDEijnRovAnkAQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			LRvtJCLhMaFACH+vGxK4pG9947ZBO66Lw5XAbw1wubA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			FN864w6B52IL5ru3qeQgg68a4E2UzxIDVl+KPAVCrOA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			IXTZThwdWtk3F7nowgVp7ZWor1Gy06srzpnxqIcEnA4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vEy+TJn9ZavqRfva8ozB1cQhGSgBJfu9XCwRiSrkYLI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			+L15rlqQ5TkNd9wxyzBlsPk8uIE8nmeszscuLbICegg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			u6zqgdT3o6fzwDYnOkU00x2/i2tcyivMTADLFZPPA9g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QVFDSnFPyCIoZ3w5sHkIxOGZUvwFjibnw+urdyTODHc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DNtZ4lXM19z0r4R8mwIK6u54zn/PXyFOvPEjMorPnyQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			1weh8DUUgG5xTwHL/LfJ+Zc6zcgMLWe71Ob4UiOlCVI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			i/yjSGmz+aOy/HGwLLrEFRKvbR+KsX0lZOZTIPiO3hA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			RPsEtccrWEtig6mbNHiWkMYntQg8XfbotberLGiQPAY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VaotE7eJsxJfXJ0NxbbjqQ15Qm07eCXc1gT1bUxuNqI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Uma28YwxRM+tvLex0n8KfqocZB/TszkF5C5FSf03N3A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4/BxxjrEOvZgYVBu8sV0w1979IVT+1FYrkHZIwwaEN8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			45mFxqI4CGtUQnR1UZyeAoV1BwfbUh0YIOY5cjwBw28=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			bbWROWJ9KavTbzjtLg3iprI0p9fmgcfbr4uIjxysSaU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			pDpbWL/Fe9cjsSu96p9uGpITYLNtLVLEIPNymXiEQtM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rqcW1JDDVDliGo8Ayn5Dl+8ccEKOIGxQNrevJfHD2C8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			E7XLSB4CFqj8KL+p0PawYM31xFez4SQ1yoJusu9SsGg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6H7AdvlQ/NWBieNi4VBd1VsMj0+n3RqTMcXBEdLOVp8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QH/A/gbSoFfpugEJ6pNWyrOPJ3VtE17zsGqFcFthb1A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			gmM2Q80yZUORWsxdKKY0tXlSdM05l005VeUdczC6kzg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			J/FuPdArIhLEmA6gm9wGjPAVhKG4u5FFbAP8q6vgkx4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			PgZzY/wHZi6+UrphfCqtNkkg8q85WzQWKXQAhZrNeLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			nTPfbhz90s8lU/XidY9FfXEMr/X4xplo8mZazNbppv0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			YZMwGSmEqA+TrG8uTl6qRj/T3dx1wfZfOXXzPg3XoLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Jry2IEckM5YnF3EtBFl6YyZMjkREWUMlZcTBE94KJAs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			H7mj1S1DLqLWzUOSfOv59Y8wmiNuGxHSD+jVpfuUTm4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O96a5+r5vnmchLKqToDXi+isusoeSG8Qub3ULjrt3LI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F6fUXzuC8qQuHTaxPbXO0HeUWj6CcAlHzR+APdKmDb8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O/tCxNNtF2NpOu/Oh/YnehGtWnVtaR3tqATZ0O3LMJM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HxrUxAebM7cG6UinNajDBC9AzGgGXEjCIND1b9BIwzs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DA3xe/7OiXodp3ZcgiRTsJhmVzAozsztE+Lv7gK8zMQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			onF64J4M8tVmwkXcXFiJ0yZmG0DbDV2abZW45rDw51M=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			7BG/1JxxXNifudOHoHz1QmHg9KHM7BqBDgLHs4rS8oU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4Sko6LV1TUnQ0+eZE13itIC6hLXbqg41DZhG+mf5Q+w=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			asDxhFpWoaU3uabZvLck3d89Ol5hh5rpJZMbHAU0+7c=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dT3aUYp+n23AMJchsfquWMlmH1RYAdqfBHKDkfcL4tA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dU72vzpWQiirC1bd45FSHcwabIPPuV1LdhFB5x0ujoc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			efZHDZvr0wgys6nKWc0f3KKMW+Y3O9AdlJ7uG6Uap6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rZOROf9xQJcL3j2RGA98TXA9f89noCxxOS3hmA3FYOQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			M8YHKgBrpOlRPXt/09CLHHRcoQebbXlsNrKlro5K4Cs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jXNygyibr4wI7x3X5Hpsd12s5IBBnF4qktbA6Fu1s4E=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			x7A3fzDkIEhJLkcQ/loKVPqYZTlbimdI99rFO5AShPk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			xAygFLiPl65irhqBbFljse1DKnfYTYnDp2S6FciiNwg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DgcySAM4oinMOtTN3gkCGgqBkC3G7ftfEiA+Kv9EZo8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			czQsJ89V9iXT25DF/I5zQP/fhaUYctv7HQqMseQ+xdo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F3Rb3SmXeekdQdsM7ibNxxMto2ZpB6lCELWRztWlWts=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vF7RZNFTIUBLvcrQ1kfDIv+rFllGIYLb05RUOdnsuuc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9QLgeuPxnM3DHkNASc/HM91d+FSHwBYLAzHkAkGtAnQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ATs7KRHGa8HKVOUQgUr0lUwxDaEHN/myokdNcUviqzk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4dIHkXqjSD2REOJKDMDNHg5YQ8i/yQHP7npthy3ZRak=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HNz1EMOEZOUoTtz67DNOP8UWI2wco7mrkcqHjCOGaRQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			SA9h0OGnXe5Zv5pm3gu3j6rk6H/WMX+TSAQSEjJ31EI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			EY6hYO8p4RtG3sV68sREBZNN2KfEnSvIuQyU6LqmE4s=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			JKnTef2jnyvMBYDKPgvS6Zriea9eKEHJ59vn+THRnMA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			cUKxEguZPWCRGXV0CQ/gS+PqZP/DrVoWekteC0LJ8GI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			u7cpuQb1/Dt+5mlLIIsgbRmp1NxXHiNbnJTc3UoyOio=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			D4tTCtDey/jdgdqCkbiw+XbGQ7WiktuEaAsx7Pvl0Ao=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jzCJ9LLKR7esTLeDdbK/rAEmgROnxn0CD4tbfywlu9o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jQtqiCt0LFzOk4JBMoYGwRHdoMuDM06+3NoXYF82Qa4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6xNaiVGfLgBCgt7SGxHDr3zLIyDJdy8t99GkobZ05JE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			j7zGPLKJr6rhW0OHUsF0b0E/O3m6WEXC71K6EQT4vaY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			97/5gije2YHsmk0dDaYiR6jSPxWJJuOsvsPM43nJmMI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Yz9ePnW/FZDJSrnL81ONDwp6MZ25AWmTkIRS2QPZxP0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KmhWKY7GKaFr3ZJHEd/j87HjqILd8EtzEHhdg+wNVmw=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			aPIrrTDaqBshWSVBbBzIM2Czu4fvw0IFiSlzGsZ4/zc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ca6ArftDe3vIjzx2/TcHREmzUm56pXdtK5/VpDwGb6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			zq1esrC0TvQAP7yy5JygUDmSuh1lQNEay7uE/bvW55o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9wO390zG9fqpWfUcdXyUYjZ34nATvK4jvvugGjkmRtk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			TJTn++GDN5gFBW2WCrYk14h55DJ4Ji5Na5ireOX+/qg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dQJYfVLngQIo8uy0WsQxnqD1wAi3rJEFO5IAENxt35Q=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			c/0rXhQwnYwDbTNPE3ue3x97MtvUVJHPkxhIGFgtBnE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			WRi14d7wl4Hv0odRtog2ZaruKfHSRPCJHt7Nqb9qS2M=
			</data>
		</dict>
		<key>Resources/_tcl_data/history.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			yxY0mAmW/zk6ADSvpe0t/kr8LislxOOZx018PcuO4j0=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			rL/5te91eQkguVAjFW+tgLGK/4yvxKbcA4k/k4jgU6I=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QgxLMIjJ2s0hvDSAEcrGHXyyg7m+54rnLu12SrCUZRw=
			</data>
		</dict>
		<key>Resources/_tcl_data/init.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			N06Og9UB3AKD1LbK5g2WtGFBBI5aV9FQDdN5+WaHn5g=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/af.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KYGWW9I6k6CetbSjNKyxXQBkXWRcWWpeytuIv6C2qQg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/af_za.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			PC9fYx7TYD7w1byzHFGyNTxcJ4OcgGoDbztwB69/Peg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			v5hOx89hnnAP5+ADgf9Yq+m9L0s91iLrLtrMxeZoEFA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KacOrEOx86oYnYrk2SZY4HeDlluuQX+2buX2nPy1ZPM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_jo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			eBFufnBsfR4+dEYJRwmBn7OaUMKiMC+S1qSY4G7Uoxs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_lb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			fJcO/rVcU3WBQ99CzEUqNjL4BUh8pp21fjfB9HinVxs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_sy.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rvF7lKDbh44vD7SdmCBXxbZjKJ46jg4rGV3Ow36FVbE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			15DlQhekv5p+HctPM5m1hhcokY6TzT8Atj8TSb23HFc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/bg.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			5zfY3HJKo7nsBxZcE+hijGqKwegDReENx34fxipthvE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/bn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KX1NfK5umds8pu55NRlRK/9lATzyYc+Q3tTSjT1Pgm8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/bn_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			fTqVZmPFKdB8ipYQQUNW3nF/Oios6bMxsFI2cnCs6pQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ca.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			FP9WT6tYRXHpVL4g1hwvrLCW/is+82nMXst8JcLZLVo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/cs.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Sc9FLu8LiXC8Vqe44EC6CIIVUIIop3Ayy6ADVSJBL4Y=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/da.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ylj/W6qWgdkWLglOgzRwB3t1VbsJ7ujo3UGIGxCACKA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/de.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zDZylpwd0iPq3ZoibgDKxzHYJFUyQIt1q5pw6e3ShnM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/de_at.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gFE6mWmhKo+wGALW/DAVcSpO/dpkVSkRobs+p6CY0Cw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/de_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			dXNYHewn6QsMfTQFfZ9O+JcnMX1V8sTgQopHdA+x63o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/el.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			my+RvjQCT7z2RfbvkkYOX5RMpqFiaLeUeKuQSyk001c=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_au.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			uFyaNz/w8DYVFDJlLdVcGCsHBL0GJeqEvtFyfsDePdg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			WWrAIgTIRap0RR/FJ2RVSfKjMYy2MFH8rLK/lI/Xc1E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_bw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			NAgE9ztiBoaraYsiAhkdaSJ+c2sWUiccmfLP7wPXIpY=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_ca.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yf4iI8SUmsChk/Mh/A/Xw0Sp5JpUsA+KTDBAR5hlhjE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rjitVFIxSwlGxcudPInN/CrSFOFG62g7jQzj/oQHD+E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_hk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yb4smtMdUWtQjQHoW8yjdar4B9bYzXxlgIXVAHBp//0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_ie.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			54aMgP1Z0YuxU0XSn1KShW9jlVnP/ULuZJwWx5OL9Y0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ceU2f+g5r8QzjFDUUPERco4JdTjsrMwbF7ECOAAbC7E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_nz.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gS2yBOTLgmYgek6Uj7o90e/k0HG7t5P5dDpDIKHO6+M=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_ph.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vhB/X64eMD6nZgdcUu8hRu8Unto3Zid24Y6TaFsXbNw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_sg.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			uNNUUZvU6xAE63sl9OI/0+5/UzpfSRpG0Z/VIO00yTA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_za.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			mM6cpLtZC6X5ItahluU4HhnGTnaCzb75FPLc5nRaczI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_zw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			kQiLu/WKcEGF3sE9vUISlrvScaGuu8s++FqZzs2Ej/g=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/eo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			UN8+DmaVAu0I3XeNCv7fD3GZO+OIsPyqEGXRyRvSLYM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			//LwilviAsgeRp4W1N4figwc/lVs2gY9oHEnnykxSDc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ar.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			r1MKzWlnZnjJW4A6KaRGQu0tLy0HfPD0e1P/JLrAOy4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_bo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			JLWN44zUyyq9CNHtpslFT/3n7RozNntFfXcCQ0oKVe4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_cl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Eq0VRus5GYkQXYC0GodobTswYm0MQqc3BfM7LXEZUMw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_co.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			G0l5h0w/AlMX388LBvyM7ggKKP8+jv4d6eiZ9tT00h4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_cr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			esX8NbxCKlRFYD4EMCNuYsyjVYeHgR3iIwX3LUOetLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_do.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hHwUwpfb5NhRfeuqjtVV89rt+EPWutH0EVmGMaC9NQc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ec.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			IIbujXOY1eYOXDBIhDs4hDe9byUH0ik8ohiTbjv2Hlk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_gt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			G8Iq+YJn1jXj8HYVomSnFpQKKx+qXKo6/1TUxaSjQ3A=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_hn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			otJYgMZDCVUqrO0ILe7R7gBkgqFMq5fbUk6Zg+6ErPw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_mx.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0Pqp15l9Vpa/+SOEFE4LnfsuTDg3WBdhP4GonAbsY4M=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ni.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Syi0aYG7t4y9KyIGDi3QGMZvz/HO5SdVQlrUkAqQ1sM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_pa.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			aDLcWrn2EIg3hM9wJpH88WhQZRvBxqd6DvqB9DvFCaw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_pe.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zIJsk2gu8Z0pq2MEZX4HgCxwzxix5eqZw0gN9tI4OYM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_pr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GVYyJc54dWlsaqLBVuZDgpLeQ2tY+NfCMlPjEyBp+aI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_py.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			lP9kIBwnqwTzYmF91Wt9hbIjvMoHNRJBludmknDFkfA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			4yaMlem31HH1/SQ2wXMY1aeWIgujnOvrzTn7sBQaSc4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_uy.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ulV6PGVidaDIcPuEZvIjeFD1p88tABkZiWcluz0+qks=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ve.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ZPeWxePjAESKHzCaDafUNUjMQFEQNv86PgyRfjIUfWI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/et.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zMK0c42xb6+0i/x3yeL4vhe8GeQUDki2Hz7xznyfOow=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/eu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			fcxJZqXBOlK20dtiviALm1od7LrM/K8VBF3QOiw+P6o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/eu_es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			k1FkotLRSBWQa0OFYoibMROVGbOo6Ns9KsFSp37Fkdw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fa.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+cpIGejIsETX1oyX/Gfg9MzWJF4wAkFh2rJND3w6loM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fa_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Y2DODzHuWT4xGydfPB8e1CfiN/MQEKQoDvLFiqbyYzo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fa_ir.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			z0ksvXOmwjByUiXXBWa25G1XML0/Y4eXgd5EM5ZWIL4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vg0tzgjmzXhrw7B6H7GtxbLPEgU8merN2qzduIAt+5w=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			8kndFpjtFofhNlTATQi4KRkwJ6L+zCQiLshUtZNQRmo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fo_fo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			cUW1esXAdLypaFgLM3wEpxu9bvuTr68pHBNh/XANx5E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			eofkGLbY0U2MEdY3CLONYH0o992/OWBsfY+6Ir54kso=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			mA5wPfse7efeSMlY9rUB7UJR9pyw+84PyoVVX1rPE0o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr_ca.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			8W4hLV0fboOp/E5Wh05Me48ZR+6IJhCnMZlIAxnvpSk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr_ch.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			qxYL/etcOt8HHgHHgxKoHuQiO79UcKuICXK79ZZSkfM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ga.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			BXx1wa1wZTcz3OQ+pb8VFQDzkxTosCNu6A+NXbYjYn8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ga_ie.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			IHLkjJi0gNtWdxiINkhbRgXVqdmYcKxztb/p3MbbRvQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			xxoHFpzb6ZYmFtKPOMMtZB2id+U+Z/jjpp6zIMHiuIw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gl_es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vCsEJM8nvvZ/MJ4rbf/vTTnEbxXZHBXoPgcMf9TiDJw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			dapob/kByeZuUdNujnjlFUtX7pBFeEVo9qh5jqlokgc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gv_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gjrwD05EYT6SnTJ3DtshQTK24hDocnUWJIJNpfC3hEg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/he.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0nra9067GNaWSILPkxJgMxuTrksoNCf5oNsUeoPeHVU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+v5l2wm9y4Y3Qv2ocFvNHDG1ng3Yo7NH6m3sJZbO4Ok=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hi_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KcfKNY//yvlHU8fML2O1g4YjS3VVL6MnLC428lN3DD8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nhTY9/VL6VOYPxmMjVnziELF9zQZpegb5kYLNiPnMHo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MU9BgMBd5KSGD2WvZGCQD/938SwI7dco9oygBlEmua4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/id.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			HxsPXe3gJjvYF3OnjpivVR82NhrMsxW2GMiucKX+eB4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/id_id.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			24FkO6H9EV6dVHlDqImlbfwMgbY/IbHtwZVcaITBsvU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/is.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Z2cRX/8toF9JoouteIU/rG/HFhhrmFR01tMHZOFyfEA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/it.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			x9hAAYVVhqC6sjampYeJItnEouoXmb8YVEhpNZdQwN8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/it_ch.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			LuNW/6JJGlpgvffX/r+sQmgkkEc4YVoMHQeu9r2jt28=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ja.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			H6ORprIt26X7BDHf4FB/CwdUFAtCRwDxZ19ywnmrCgo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GwAinfWpeaBAM5u8ctRI85lo/uXMJPByQcn2EpqbU90=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kl_gl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+Z2kUTiorr/ZJ0f8KJkvDDFcbErZdxDq+UJyY7/6E5w=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ko.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			9uKw0RbSyayQ3aQwtokjcdh6Ts+2lVMYl47W9unVRqY=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ko_kr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MaS3T1HFhDVJByUcVf5c6JTSyWGBVqHcb1qXm8NQ2xc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kok.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0jbVsnGEsegT5obZAUGBF/ItZwJOaUQBj8S2M9+f90Q=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kok_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vuB/FMf0/JO2KsMY+J0u0N1v8w0r8hwodGVP8CkqbEs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			9J9OHHFCv3qC/CufwHUXGuRZA/5pExR4wVIZ1yu6rTM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kw_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			eopTnIuZCu/+oGGIuY3EN/0qbon/Zkg+8zSZTnP9Dsk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/lt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			UQ2O7TBAtQr69qPIW8mIR/G01dimhcXsBqzCSRuJAQE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/lv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			/ZWzijvr1ZRovcKJC6xZ3zHDUuF/LnfIJHHhyolGmAI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			84TdiFIxR870Kqhx0yP8TL7jOP9nzFyVrseUDA5TGuM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			6y4rekGFSvaM71iBzx+/TTjnDS+rLD885ZAapcxW/BU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mr_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			91opuzI9tDVLDHWcscjFpP/Ddt/9dCdMpgo2mUgWp1w=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ms.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gx9hHuhRpkvxul+aVEHsHVByL6nxW0IncH/hkn91TeQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ms_my.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0FlI11wGZprduXCLxftI5rZR1OYu8bMn74o/YF/VJxw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0RVxiBjj4zZ4R841u1/wNh0ImT2XSdQ4yRj464etiBQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vEAYid2TTEnRDZm0cUQb4rU2sXInOcewq33nYpaA9gI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			SRKLNriOOAGIBZxLWTwxc4LzLinRrcGNWNFNFCRZors=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nl_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nNVOwky9vsXk/lQ92oypU5BnjUMtMyAfocMrYfj+Ilo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Xuk6jCRXIt62S2jv9QwIHyTaXeQ9mZwAahDEhOHTtO0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/pl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			w5WV3cAJXrSunmbbAu4XWzGsPaH2SeuI+mG5Efg491M=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/pt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nyv/o7TYeDss+yztnMQxms8GmI9hgpoeUpHVWxmFTog=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/pt_br.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			44OyBITukMAAVNUt1a9HOyrJ3FDBTUWaV5719EJx0lY=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ro.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0uFL4Yg1DTQ5J9U4DrVnIDn+mjfpqZV5IbQORhmzYCc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ru.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			wqOgvlvFpGpqY8TeNOMXtAK61Awi+yk24aT1PB4vYl8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ru_ua.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rl098j8BlFXz7fwyYqrCsACYiB8Juak0wNJsCriWcAw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sh.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			APEZcByfPronNwGmpzGtr9e4kC9rzPNOYTCJhEVuGTo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			BD3s5up8g5VrMwC5X4oOkrraqPwp1sUQcGZJ0dgQZ5o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vUiMnXkavt9pi2a3aOK/JCUf/q8G9T+zdGyrRXcQ/3c=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sq.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yycAfhODFbBkV2wXkxKAz+bmkp78Pa/XFxcT0gTPw78=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MT6M27wCiK7ZIrmSenMx0Pqi5FHUF0sfW3bFyfrsj5s=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			TzLhUYvjJw9NuAE2+sADHDhd084TP6pTTxQc9FnGETo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			a1q4riZdtDaxXTImOohw7FXHwMB0FbP5uqw39zvHBOU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ta.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MKFCpI5X8ZTsw6qSQ5MPPm4bToszGozdJwXsnCgNzLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ta_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			qlfV+1zD9Z7Go/mdelGEQDgJqjo7wC7QhCUH1CGLaD0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/te.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			QsNNAqYHnE0NaDdQs4CfNFY3vG2BRlLD+ws0S2a3DHk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/te_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			iL2vSyW2hLAyCi4R0/533d0l47FxQb1+0dY2mMSA5Lo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/th.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+WQabr44Rc5dNs7Uc3SfWQnJDFLkBfB0ptqBfvbzmGc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/tr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			33xLpnRXy0fu8PXKjgKP9Gas3Yd6SHaX3EjsrHNHrEc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/uk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zy547zMi8BIelYCY71+S2gCDRGV6c0OerGWMtr89cr0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/vi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nGkJTAvVLVroRIQxV06ujuS+MewuhgI2bfbGv0vImlg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			74G0HsafZ6OU7OKzmDtns9DIgTYkwr+h2KjBWyFgisk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_cn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hfkc9uMWd0ql0MHsqFyI5ZH9U3Flu3mSnF5qHKmeVsg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_hk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0SPgtMJhT2gICLWMygwUC6GHSUssi8+MYEx+tznHCII=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_sg.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			JoNRd2avnaDYe3qGLemt6oLZoUVPx3Op48Gm2Sq6lHo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_tw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			1YJAbFGj2x6t9lB8UKH4V0D9p9qOJ/wUOP62JCkAyxI=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			BRPILgNANTDA7mbTSraX0hPpot74bdk1zF9r8Bw7fc8=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			vEr/gTJALmJE9Jez+Em2swwyjbhRKgTkyGxSOoDy/mg=
			</data>
		</dict>
		<key>Resources/_tcl_data/package.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			nt+Xg/4myFBA3ISbeu2DUL7LJRRLayzmBo9Yz99XT0o=
			</data>
		</dict>
		<key>Resources/_tcl_data/parray.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			6+WitMu81/0/em921o14VjAdsBs1DAQJQqe4BqRuABQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/safe.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			85ul2FwgQahuph/ab2fh4CgfjW6nzAlDG4hf/2XXH2U=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			UAbqOVwsqW50NT6Uhta+PiqFp5JZaaT+QWYvCS75TKo=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			+DK8EyOFZdFCfEY/3KzvCpP03ANSwOPzW1c9mxHerKU=
			</data>
		</dict>
		<key>Resources/_tcl_data/tm.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			eODV+3flG+DJnN/f71lskbZroRYMRsSKYyZmEbvPJq0=
			</data>
		</dict>
		<key>Resources/_tcl_data/word.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			8Y6K4pqo7Sviy7VWi0JD9lT3+wqiBm4Wl3hj7hQVubw=
			</data>
		</dict>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZESFUunddehLjfY1KeZ/ltuTUdSRPsLpb9Up/kw14Fs=
			</data>
		</dict>
		<key>Resources/_tk_data/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LwE7ZD1i8I3aqh3qOf+A1mB1acnhrMGUBjd7ZNdcz1M=
			</data>
		</dict>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			NR84nwkNDP5bEiL4N4VjCh7q5U9oJ0d4RYBbuz0Rn3w=
			</data>
		</dict>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			xVB5xDQmWXvoNsi43tBKvauOHNfSuQqJivlSD4AsYEA=
			</data>
		</dict>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9izluEUK57PsK92f32fHf8yZqhjeGV3lM3x1yZeksRw=
			</data>
		</dict>
		<key>Resources/_tk_data/console.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZcMnE6v5gujrWx073RQa7BtFvYl7G2CHa/Ff7bV5rP0=
			</data>
		</dict>
		<key>Resources/_tk_data/dialog.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			0bHcykYo9h6hUqD6aCAXX2E7w9bpK3OdATKB20huYl0=
			</data>
		</dict>
		<key>Resources/_tk_data/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kvsv5bIsxpf5OW02gc89McA1mNfY0YWLS3q5dunIClw=
			</data>
		</dict>
		<key>Resources/_tk_data/focus.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QNThAaZLdTYfdjR5sBIHrnFTUzfnnObhYiZYQvZHHu0=
			</data>
		</dict>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			iqfPB4wm85bhpCGgPQ89BXMeQT/USHUOGS2r/Tqrc8U=
			</data>
		</dict>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Cwh+IFJg1eJUWVpBi6LwnbtV6WefoOjPqZR6eSFDfvw=
			</data>
		</dict>
		<key>Resources/_tk_data/icons.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			TKuArOQQSkFpyb0f2yTWl4ibmEuRopC2mkgZEsOKn9Q=
			</data>
		</dict>
		<key>Resources/_tk_data/images/README</key>
		<dict>
			<key>hash2</key>
			<data>
			JpWt/46QDDG02GQU0iuKSdbdhlyj3Zlnj6NVzcRgk6g=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			8+d/2UGY7EeDEJNVU2Y46RYvnFeUdTgwdNAkA30Xl9M=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			cvazTTyPQk/wopCnk/z780/VYwqRbNAuCl3aAUS1lX8=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			E4wkA4IwTzUDg7Au1WxpEDqUMcBUTrHsXc197HpVXdk=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			D0BHZNB6auLvnh4OjqrCeLfUiNYc8cCEFG8vM7SF8u0=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			TQvTIoq0zD5RWfQze+lp7HtzNOJlyZt2M+Pa88P8+2I=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			KUTrxK8YlJUb+fElD05u34EcIYN0WVDqmoqSZxWILPc=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			vMDmRYJJQz6MumxYEit8DvqVV8vI+1+Tku7V0lefxws=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			X8JcMK7nZHfxxOkikxzIBoI98FlSVYP/VwVwXZ6RPBw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			YoZulVAcQ2symhVDI1V0PG79ZKN8+2W87ORlq2Ps8kA=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			utkRY4Y0P0pMOUvbhxRuSfZ09ofVK7hHvZ6BmP2jgsw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			RiqP+P0FGoEA6MbAhvSX5AVqzlsgtEeR9Kq5ZLAQpEg=
			</data>
		</dict>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			5Tj49JNMpuHOKUFtKSFx8o5n2mxy7Z0ja6QvN0RepB4=
			</data>
		</dict>
		<key>Resources/_tk_data/listbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/f1M8xAtg7rTVW0OAIa18yD3RIS6v5BCHPUm/YufvrA=
			</data>
		</dict>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZVQxBBVDj0dyKm14mqig/8z0xfaZQSwtacMs/+bRnPQ=
			</data>
		</dict>
		<key>Resources/_tk_data/menu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Oek6K9dbkVYjXzJx6gL9fKyCtUKuVt9zPNe9YqLEIH4=
			</data>
		</dict>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CsnRHUBG702ObSGfaUG/acauRIxqHC9/w4L4S1eG9mA=
			</data>
		</dict>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			mmKB+woZJ9e4H86ev8lSNb2I3xFK2Kh6/qjqawlTM4o=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0bD+0L6lGz+vCNhjQDTHOIvnFI+bgHRgt9GFcG24QW8=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hebO5gAZJzdnJfkeqlXRez2eOGQ+F3VaQsBf5JHGO94=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Bt11cmJt9csKjTr/usm7dMsSRpB2g21m/RmuW1+rQsc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			HVbQp8B9NLuBZcukf6STUbi8Wp2yRCkLlgHFiF0WFVw=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Zzx2pIraCaFUywOFNL+Q47nAul/WsWGdszUH3mVVM2I=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+5PUVanZzz+CLJaN+yc+2THkM/JJTXHWtfjYPd5+rMI=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yyS6lZhmcXdqg9zwJWJjgJBx0z7dnAY4Oxn0w2+CCTM=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			kdxHGNyFZsNuS80MKSwB9GfKdmHv9gG4cKvN/kqU7Ls=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/fi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			9KLE2npnectQiwq7w988lKz3cZt7Ne31VSha24+PzFs=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Cou7TR/Ye/epDd+lD0ckmUyc540fPpHPQMEXfbeUHcU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yBNOrRKeROnFBD4drYGmqQDw3nHbNGjiYDhAA4aH8dg=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			n4PdAwntYhEA8xh//NrlC3X1lzu+dK9VCnjvABBJXe0=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			09B6rXksDoP0cEswSTHqVJ0Sy7PZmlc9mBXpVKVxBwc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GZO07C3ACdLmyhhdC9Vl0/M6Tvp5uso55Pl/V01j8wU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rQ5GYTHTeJ3jIdnQWI4Z5GR7qC7eQe7m6+9GR4b4vb4=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			BhkL8kYjafatTMuN7l9ln4SFbOzPKAbd35UR4BU5DCM=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			t26/ohvB6TegSgTlEivmS1ze4fR8cFi3HYuSPXDDsXs=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/zh_cn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			TVsyB7WkBymheinas89+5j5T+cB90FlKvwr4OioBoXg=
			</data>
		</dict>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9pKaXg0YvExmZiBsY6xKqmbtxLn0Vt/AgzAM+pWkS80=
			</data>
		</dict>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			dtvb+SFmeNSNFkD4/R4njnFASC4crHaAEnqaQlzGHe4=
			</data>
		</dict>
		<key>Resources/_tk_data/palette.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YLhXk2i7MGPxbSXwBzhREeDvjZe7KWsDZW3BduNR48o=
			</data>
		</dict>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			St9zixdpFInHHEudmmSxKWGtqGZ7gYVvetvGHf/q3yk=
			</data>
		</dict>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			AvgXoZo7HxV4BT51wGsrK3yu8+9FsBxBs0mCqlMA6BA=
			</data>
		</dict>
		<key>Resources/_tk_data/safetk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			3bDNWcaPwKwhmQCgTfDMtXZEDdiBLEG8oguXRYCGCJI=
			</data>
		</dict>
		<key>Resources/_tk_data/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QjVGkzJp+uyRMAHyvOMLvbzjkn3J3Zb+dIE+f/snz7U=
			</data>
		</dict>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			GJ5+5LZ4YQAccUpViA2zSs99YmqBbhiwSyMq+ebjPoE=
			</data>
		</dict>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qATIMCngTmvTTTNSYPOIiQ12V961RBc4WL0FAY1zqwE=
			</data>
		</dict>
		<key>Resources/_tk_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			jo7Oz9YEb+QT83qRkz7qCG4xlZs/vrEnr90FzZFBvpo=
			</data>
		</dict>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			eTDIeB1kOMEeOh8PPTwdBS6SS9+3/ef8F636uc4+QQI=
			</data>
		</dict>
		<key>Resources/_tk_data/text.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/RK8o8fc7fP8ZkCQKvHJ9IvF3yfJFT7a7BVBPxWB5Kk=
			</data>
		</dict>
		<key>Resources/_tk_data/tk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			d5znCZZ10fzaqKU99Yz1stptXwPNv3qMMYhgvQGm5G8=
			</data>
		</dict>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			2dmLyZ2Z0KmIOrUFTe1Rmrf+Rx4NHSRgpUN/I1rIyVE=
			</data>
		</dict>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			tYHxUjBBC/jYxr36NppiZw6JtVYqIhcVaxk7PORnIVM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kri+nYk0hQttJAuXBgOwrXxt1KRRNFRWlPtSlm10KGE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			vDFpQ5bs+YpgQNMohopIPB9ncJZAa1DqidWy9CWCiNY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aE7UCpYJyidc5vPlR/vymA48BA+t2vD9ObfL19WYc2Y=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			GdSa3g+4c6hvgMVp+7F+LWZ6YpIEVWP+HPFEnNG2jWE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			x7H0DXeCD7ryGV8rs/M0s4/sZT/kdlP54woBrUymO6U=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			7Fhd4oODfR9ZkvgtYyrKrSqgiy/xnwvbGaLl02Ju4Xc=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			wduT6vSCvuG/V8tCPyioxncM7Zs367uMn8htYSFf6Og=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kl2OcknhDLnjrlqJ/d3ShCXHItIYcV8326wT6TO9AZ8=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			nDQXNeF5J5dmJA3Im4O2qeusYIkAZSUf5c9Dj4sU2Tc=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Hy0pZbLHKulTwIKaInV3raiVnSRQJjizGGqoQLZ4zDo=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			oW37Hq93sFoO+StMx9APVZ+2zwsvc1w6+sUDA8UkoIE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			i++TNTpxNENNx4Dup3tS723IU6GT6NgaEy0OJO2TI0s=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			5lC3XhUGeKadjAXnyRXtddswcW9AgorXXRnNWI/ZD+M=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qcNPWV5UfOlO5l4nxBUZXSshBlOp/8+zlVnF4PqcBvg=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YWHEC/XWz+nINqqyNw78lRvD4UvBfdlHdFW4a1JS1pY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			7lJh/g/APlT0F6uwMuUokYX4aY1grnTbaQMv7estEqk=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CzgY33zw1v8XucWImrcwg+Z3KcOg1rj+DAYwD7bS3D4=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			UTLjkRKRZIbEkBjBv/unDzDfYHsQPiKuXUPHHavoy0A=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Qvy3Awa4rUg/omSdfJsTiRsrhB4m0pT1PaMi2kiMYlQ=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LViSB46jMef5Z/RqtRDxPokMnuGF1ZfZ+zvEwp5ADs8=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			0XwT0tFjob/2mH91EiDPPc8NRmzGxSqOLMYhnCFinhQ=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			4kAUxXeTlednvn1DUCL28Wn1CUm7vtfnmy3mrmXgxRM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			BfrQWCgOeolHqfcRIrRCuS19V4tGGLCL8Lcbbaxaoi8=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			jktM4LNYRdmGqTrVZM0SOKHoo5AuklkAMFjwrsAOMMs=
			</data>
		</dict>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			402CjnQPFRuWAik0qux7uDQ+I9BA+1TARkGIj1F2frg=
			</data>
		</dict>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			IdJZTIMZ8UxHoPC/PSrKvMgsmrjYSmT6PLYIx+dfWcM=
			</data>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			NYa8I7UlxK8SW1QwSqejtVmH71N6XmMi+vdZgWSr66A=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			+tZ+KwYMMYtshkbQh/vTrdk4tmdiQ/FLDFJiMXlkEnQ=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			SsjgNgb/pMN/YaZRCiCA8fN6cFT0cmwhSIfTsj9y42k=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			MZz/bnox8PKkHEddykKJCqXRn+FgF+IpD4wdThT3ZIE=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			EjTAF8hx6y4g029mj5PgZs3Lk9tGTVzvnXpb+DUG0ow=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yUF0cMFs7XpD1sSo4Cevpu3GLCTVrufEwtzRE4WWTTs=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alf6b4+4lhowzmQpUisYDXbjr5uODarCWQWYQThqa9M=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ea2Gv+p/BVesHiCAKJKrtEqWevFbkxWwA5zXXItyp3Y=
			</data>
		</dict>
		<key>Resources/img/123.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			zRbt2eaNE2k5bwMRPx6qaxe5d8M6NSKc7Qjkbrb/ODY=
			</data>
		</dict>
		<key>Resources/img/bluetooth.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			AXDfQ7PFxvX7gGQYsojZ3L4PMUU0IIMf0Vbora632Mc=
			</data>
		</dict>
		<key>Resources/img/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			/wu7jVkhEKGPoKTYum8zEAUTPQuFfFMXP41QTSed88g=
			</data>
		</dict>
		<key>Resources/img/erase.png</key>
		<dict>
			<key>hash2</key>
			<data>
			DltqZK/wdHkI4bv8nRiuvrd1BdOrrb3uBTsOzFcZRZ8=
			</data>
		</dict>
		<key>Resources/img/exit.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b5d6vlIoz/sJxS4j+nsUsX46dyNReM1kJkVz+QSIJJ8=
			</data>
		</dict>
		<key>Resources/img/findmy.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			6O5TLJgtkHq7LHYtkdu2JJQppDEmUUKnPrSZurSZ3Ew=
			</data>
		</dict>
		<key>Resources/img/shutdown.png</key>
		<dict>
			<key>hash2</key>
			<data>
			adYIbIXC8nbpXDrEmKQUQo+zPZY5R8nBZJbzWaxWFPc=
			</data>
		</dict>
		<key>Resources/img/sysinfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			vZn9xKNKjSY5I0arOZQ4oRzT4WN8azNInwkwJN8dE2k=
			</data>
		</dict>
		<key>Resources/img/vs_ade_checker.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vuFlbfGrIEV2gk+uuiuwLfAsc9VuCo4LT4bVuAJjm6U=
			</data>
		</dict>
		<key>Resources/img/vs_icns/A simple and minimalistic image icon featuring the letters "V" and "S" in a very small size that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash2</key>
			<data>
			rNQktSPlSyE+VKfSgMGXqpOZTyTJd6h4JNlIUpdDoyA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/ce5ccf55-e450-46ea-ac98-70d6b5a7bdd1.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			pP6U2IyrwYxqXce/NqNnp6uZ/AyYydNiJlw13ZKwobE=
			</data>
		</dict>
		<key>Resources/img/vs_icns/checkADE2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			WqaKLAzi7FB7Ewbroi4ebjBga54GoM94tWkm7qx3p90=
			</data>
		</dict>
		<key>Resources/img/vs_icns/outlook.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			tjT6ORXV56DHmCh07PXANHtyqJKen6F6ymXv2Z5QlMk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GeEl6CBzyDu50Hu4C2j+XLVl/4jQbIC0t3SlJyK7BsA=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1ju/nmVj5Szaqn5wjFVQo4KMWcXB/CjOpYGSfMgk4Kk=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			sTzAaJlRqYcg0y5qmy5xwuyQ4gRCnPggVcJIdWnX8i4=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			FJBSpYLuVVrMudR2GNQ0yVHsWgz64STZc5AUwJRJOOU=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			He/b6Cc/DD4cLlU4K4nqPm3/r1AILuwNskrylo3JHbc=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_ade4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3XK2IiySOO3EzgPGPNv7hpDmm7gIKTs1mkCU7SSaGAg=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vsed that pertains to a Mac OS system lock checker. The design should be sleek and modern, emphasizing security and compatibility with Mac OS..png</key>
		<dict>
			<key>hash2</key>
			<data>
			NpKcDDxjT73l8z1/Nc1X0EFty0BKBNJ7s7LIbSUh3r8=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lrPqU4byxTwkOHY4msSrYvfC+sUcrU7P0gZ68v7Cn2E=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CD+ufxpcMTBVE8LJvKzniszXAhS8xNABGEo6MHqbwPU=
			</data>
		</dict>
		<key>Resources/img/vs_mac_tool_v2_rounded.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iecaM5vNBiUb+RG27Jy3/GUEgorL3Cd8XVY6gYkRcFc=
			</data>
		</dict>
		<key>Resources/img/vsmactool.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jOZag2Q3yCpPYvFFXgOuK2Baf8a+HUylmWUhBDhBI6Q=
			</data>
		</dict>
		<key>Resources/img/xxx/GenericAirDiskIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			yOfFn7nhq1viUuO2rlX5hyia0vArQ9JV/8opaAICgTA=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarDeleteIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			7NPvIdku/VrKm7yv+ITV0DWiNJ8o/9GzDdwNyBPR31c=
			</data>
		</dict>
		<key>Resources/img/xxx/ToolbarInfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			rblFkpNuBhHlkhAnZmha0YXeMvlvn59Z1WHiRV8i9oA=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth.png</key>
		<dict>
			<key>hash2</key>
			<data>
			n+3wBQtqTyxoYteALhMKiZcHUdA1B07ssRXWRE88dDQ=
			</data>
		</dict>
		<key>Resources/img/xxx/bluetooth2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8IWgML4+zBqL3Is226iqcU6EUotB/sTfNaJ6SfeMef8=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			1V1jeQug46T0y08D/mYq79KveXSvkHV559jV7HKjgF0=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XsQmYdvHwyFU0dtXw2caSJKrbExuSwrxFFwUrvUeCyU=
			</data>
		</dict>
		<key>Resources/img/xxx/checkADE2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OEJKmlGH50iAIpFiklwgppToeAxlRc93d70whg30LxA=
			</data>
		</dict>
		<key>Resources/img/xxx/clear.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JMy6eOPBIeGPyt7CJb0POj5Gk/27KHY/7WH+EncDbgo=
			</data>
		</dict>
		<key>Resources/img/xxx/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			EVrTPOkfu3b6zVGgdbFJqeL4YSUjlAaHNBfGUMoNb8w=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			LDnYsUI7Tbe+oZvhEh+36wBZLpo3JsKIUU8tq3vcx1s=
			</data>
		</dict>
		<key>Resources/img/xxx/panda.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			8s+4K1yGSugGA3D+rfgAcSIaVxl7b1YT+GN+tiwKA1A=
			</data>
		</dict>
		<key>Resources/img/xxx/xxx.png</key>
		<dict>
			<key>hash2</key>
			<data>
			rrsBqmDhsnewSY7OzcdCmv3LFV8XTgh4kDnyoL33s28=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			htoPAa6uRjSKPD1GUZXcHOzN55956HdppkuNoEsqR0E=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			rS2xXSTtNujJISr88RqlBhI4T0Wvx4yWyQLTSGSZ2tM=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			q8oa0Yc1qZEl7CfWXWIMuJInT2LOFHtAv7n+Lqp1eYE=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			PZUExdf71Ui/so67QXpySuHtCi3+J3wvF4ORK6k/S8U=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			8ibyc9zH2ST1JDZHWlQZHEUPx9kVaXfVy8z5af/6OUk=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ohh1dke28/NdSNkZ6nkVSwIKkLJTOwIfEwnXKva3pkg=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Resources/libcrypto.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.3.dylib</string>
		</dict>
		<key>Resources/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Resources/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Resources/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Resources/libssl.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libssl.3.dylib</string>
		</dict>
		<key>Resources/libtcl8.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libtcl8.6.dylib</string>
		</dict>
		<key>Resources/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Resources/libtk8.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libtk8.6.dylib</string>
		</dict>
		<key>Resources/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Resources/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Resources/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Resources/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Resources/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Resources/password_dialog.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jKoWHr+PIsGwZpLlIzCb5FJrrX+1xVN485fkgdO4D80=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			anuQ7/7h4J1bSEzfcjIBakPi2cyVQ7y7jklLHsBeH1k=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			DY08buueu+hsrH1ghhVSQzwynanqUSSLYdAr4uXmQDA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			mguMlWGMX+VHnMpKOjjQidIo1ssRlCFu4a4mBpz1s2M=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CO3fD9yylANiXkrMo4qHLV/mqXL2sC5JFKgt1yWAT+A=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			N/7c/79zxOufBY9HZ3yzMgOkNv+TkOTTio4BydrSjgs=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform-1.0.19.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			zHmOsmZZyxJGnPkfLnlVW0SXPdZwe/6/eqK52Y2AaoI=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			RmVOWMVr/zdTEOOLvFxo9kLAmeCclRtbrZrIHYo3fgE=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			wpWJWWBp72mPE5QmLQGfVwigWlIR2P7Oqkw6t09dfZo=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/tcltest-2.5.7.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			32I9ic7w+l0sU8uazh7o/0r9WnNatthuaHV9qJEi8ms=
			</data>
		</dict>
		<key>Resources/tcl8/8.6/http-2.9.8.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			dR6lWt85BSAdl8h4jn16thgKT+Idr7h2B55QH6RI7KA=
			</data>
		</dict>
		<key>Resources/tcl8/8.6/tdbc/sqlite3-1.1.7.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			RFO4Oh4rOFyr8wbSi2p2UN4ac4yGK7jy717qKvlpCSI=
			</data>
		</dict>
		<key>Resources/vs_mac_tool_v2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lrPqU4byxTwkOHY4msSrYvfC+sUcrU7P0gZ68v7Cn2E=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
