#!/usr/bin/env python3
"""
VS Mac Tools v2.1.0 - Fixed version for PyInstaller
This version includes runtime fixes for CustomTkinter asset loading
"""

import sys
import os

# Fix for PyInstaller: Set up CustomTkinter paths before importing
def fix_customtkinter_paths():
    """Fix CustomTkinter asset paths for PyInstaller"""
    if getattr(sys, 'frozen', False):
        # Running in PyInstaller bundle
        bundle_dir = sys._MEIPASS
        
        # Set up CustomTkinter asset paths
        ctk_assets_path = os.path.join(bundle_dir, 'customtkinter', 'assets')
        
        if os.path.exists(ctk_assets_path):
            # Monkey patch CustomTkinter to use the correct asset path
            import customtkinter
            
            # Override the asset path detection
            original_get_assets_path = getattr(customtkinter, '_get_assets_path', None)
            
            def patched_get_assets_path():
                return ctk_assets_path
            
            # Apply the patch
            if hasattr(customtkinter, '_get_assets_path'):
                customtkinter._get_assets_path = patched_get_assets_path
            
            # Also patch the theme manager if it exists
            try:
                from customtkinter.windows.widgets.theme import theme_manager
                if hasattr(theme_manager, '_get_assets_path'):
                    theme_manager._get_assets_path = patched_get_assets_path
            except ImportError:
                pass
            
            print(f"✅ CustomTkinter assets path fixed: {ctk_assets_path}")
        else:
            print(f"⚠️  CustomTkinter assets not found at: {ctk_assets_path}")

# Apply the fix before importing CustomTkinter
fix_customtkinter_paths()

# Now import the original script content
# We'll copy the entire content of vs_mac_tools_v2.1.0.py here
# For now, let's create a simple test to verify the fix works

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox

class TestApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # Configure window
        self.title("VS Mac Tools v2.1.0 - CustomTkinter Test")
        self.geometry("600x400")
        
        # Set theme
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # Create main frame
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="CustomTkinter Fix Test", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.title_label.pack(pady=20)
        
        # Test widgets
        self.test_button = ctk.CTkButton(
            self.main_frame,
            text="Test Button",
            command=self.test_button_click
        )
        self.test_button.pack(pady=10)
        
        self.test_entry = ctk.CTkEntry(
            self.main_frame,
            placeholder_text="Test Entry Field"
        )
        self.test_entry.pack(pady=10)
        
        self.test_switch = ctk.CTkSwitch(
            self.main_frame,
            text="Test Switch"
        )
        self.test_switch.pack(pady=10)
        
        self.test_progressbar = ctk.CTkProgressBar(self.main_frame)
        self.test_progressbar.pack(pady=10)
        self.test_progressbar.set(0.7)
        
        # Status label
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            text="✅ CustomTkinter is working properly!",
            font=ctk.CTkFont(size=14)
        )
        self.status_label.pack(pady=20)
        
        # Close button
        self.close_button = ctk.CTkButton(
            self.main_frame,
            text="Close",
            command=self.quit
        )
        self.close_button.pack(pady=10)
    
    def test_button_click(self):
        messagebox.showinfo("Test", "CustomTkinter button works!")

def main():
    """Main entry point for the application"""
    print("🚀 Starting VS Mac Tools v2.1.0 (Fixed Version)")
    
    # Check if we're in PyInstaller bundle
    if getattr(sys, 'frozen', False):
        print(f"📦 Running from PyInstaller bundle: {sys._MEIPASS}")
    else:
        print("🐍 Running from Python script")
    
    # Create and run the app
    app = TestApp()
    app.mainloop()

if __name__ == "__main__":
    main()
