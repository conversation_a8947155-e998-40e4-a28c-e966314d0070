#!/bin/bash

# Script to install VS Mac Tools to Applications folder

set -e

echo "🚀 Installing VS Mac Tools v2.1.0 to Applications folder"
echo "======================================================="

# Check if the app exists
if [ ! -d "dist/VS Mac Tools.app" ]; then
    echo "❌ Error: VS Mac Tools.app not found in dist/ directory"
    echo "Please run the build script first: ./build_vs_mac_tools_app.sh"
    exit 1
fi

# Check if Applications folder is writable
if [ ! -w "/Applications" ]; then
    echo "⚠️  Warning: /Applications folder is not writable"
    echo "You may need to enter your password to install the app"
    echo ""
fi

# Copy the app to Applications folder
echo "📱 Installing VS Mac Tools.app to /Applications/"
if cp -R "dist/VS Mac Tools.app" /Applications/; then
    echo "✅ Successfully installed VS Mac Tools to /Applications/"
    echo ""
    echo "🎉 Installation complete!"
    echo ""
    echo "📍 You can now find VS Mac Tools in:"
    echo "   • Applications folder"
    echo "   • Spotlight search"
    echo "   • Launchpad"
    echo ""
    echo "🚀 To launch the app:"
    echo "   • Double-click it in Applications folder"
    echo "   • Press Cmd+Space and type 'VS Mac Tools'"
    echo "   • Or run: open '/Applications/VS Mac Tools.app'"
    echo ""
else
    echo "❌ Failed to install the app"
    echo "Try running with sudo: sudo ./install_app.sh"
    exit 1
fi
