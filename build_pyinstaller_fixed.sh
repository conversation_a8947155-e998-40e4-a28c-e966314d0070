#!/bin/bash

# Fixed PyInstaller build script for VS Mac Tools v2.1.0
# This version properly handles CustomTkinter assets to fix the blank UI issue

set -e  # Exit on any error

echo "🚀 Building VS Mac Tools v2.1.0 with Py<PERSON>nstaller (FIXED VERSION)"
echo "================================================================"

# Check if we're in the right directory
if [ ! -f "vs_mac_tools_v2.1.0.py" ]; then
    echo "❌ Error: vs_mac_tools_v2.1.0.py not found in current directory"
    echo "Please run this script from the project root directory"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "⚠️  Virtual environment not found. Creating one..."
    python3 -m venv venv
    echo "✅ Virtual environment created"
fi

# Activate the virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip and install required packages
echo "📦 Installing/upgrading required packages..."
pip install --upgrade pip
pip install --upgrade pyinstaller
pip install --upgrade customtkinter>=5.2.0
pip install --upgrade pillow>=9.0.0
pip install --upgrade keyring>=23.0.0

# Install any additional requirements if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📋 Installing requirements from requirements.txt..."
    pip install -r requirements.txt
fi

# Clean up previous builds
echo "🧹 Cleaning up previous builds..."
rm -rf build dist
rm -rf *.spec
rm -rf __pycache__

# Check for icon file
ICON_FILE=""
if [ -f "img/vsmactool.icns" ]; then
    ICON_FILE="img/vsmactool.icns"
    echo "📎 Using icon: $ICON_FILE"
elif [ -f "img/vs_mac_tool_v2.icns" ]; then
    ICON_FILE="img/vs_mac_tool_v2.icns"
    echo "📎 Using fallback icon: $ICON_FILE"
elif [ -f "img/checkADE.icns" ]; then
    ICON_FILE="img/checkADE.icns"
    echo "📎 Using fallback icon: $ICON_FILE"
else
    echo "⚠️  No icon file found. Building without icon."
fi

# Ensure password_dialog.py is available
if [ ! -f "password_dialog.py" ]; then
    echo "❌ Error: password_dialog.py not found"
    echo "This file is required for the application to work properly"
    exit 1
fi

# Get CustomTkinter path for data files
echo "🔍 Locating CustomTkinter assets..."
CTK_PATH=$(python -c "import customtkinter; import os; print(os.path.dirname(customtkinter.__file__))")
echo "   CustomTkinter path: $CTK_PATH"

# Verify CustomTkinter assets exist
if [ ! -d "$CTK_PATH/assets" ]; then
    echo "❌ Error: CustomTkinter assets not found at $CTK_PATH/assets"
    exit 1
fi

echo "   ✅ Found CustomTkinter themes: $(ls $CTK_PATH/assets/themes/*.json | wc -l) files"
echo "   ✅ Found CustomTkinter fonts: $(ls $CTK_PATH/assets/fonts/*/*.ttf $CTK_PATH/assets/fonts/*.otf 2>/dev/null | wc -l) files"

# Create a PyInstaller hook for CustomTkinter
echo "📝 Creating CustomTkinter hook..."
mkdir -p hooks
cat > hooks/hook-customtkinter.py << 'EOF'
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all data files from customtkinter
datas = collect_data_files('customtkinter')

# Collect all submodules
hiddenimports = collect_submodules('customtkinter')

# Add specific imports that might be missed
hiddenimports += [
    'customtkinter.windows',
    'customtkinter.windows.widgets',
    'customtkinter.windows.ctk_tk',
    'customtkinter.windows.ctk_toplevel',
    'customtkinter.windows.widgets.font',
    'customtkinter.windows.widgets.font.font_manager',
    'customtkinter.windows.widgets.scaling',
    'customtkinter.windows.widgets.scaling.scaling_tracker',
    'customtkinter.windows.widgets.theme',
    'customtkinter.windows.widgets.theme.theme_manager',
    'darkdetect',
    'darkdetect._mac_detect'
]
EOF

# Build the application with PyInstaller
echo "🔨 Building with PyInstaller (with CustomTkinter fix)..."
echo "This may take several minutes..."

# Construct PyInstaller command with all necessary options
pyinstaller \
    --name="VS Mac Tools" \
    --onedir \
    --windowed \
    --clean \
    --noconfirm \
    --distpath=dist \
    --workpath=build \
    --specpath=. \
    --additional-hooks-dir=hooks \
    --add-data="img:img" \
    --add-data="password_dialog.py:." \
    --add-data="$CTK_PATH/assets:customtkinter/assets" \
    --hidden-import=customtkinter \
    --hidden-import=customtkinter.windows \
    --hidden-import=customtkinter.windows.widgets \
    --hidden-import=customtkinter.windows.ctk_tk \
    --hidden-import=customtkinter.windows.ctk_toplevel \
    --hidden-import=darkdetect \
    --hidden-import=darkdetect._mac_detect \
    --hidden-import=PIL \
    --hidden-import=PIL._tkinter_finder \
    --hidden-import=keyring \
    --hidden-import=keyring.backends \
    --hidden-import=keyring.backends.macOS \
    --hidden-import=password_dialog \
    --hidden-import=tkinter \
    --hidden-import=tkinter.filedialog \
    --hidden-import=tkinter.messagebox \
    --hidden-import=xml.dom.minidom \
    --hidden-import=xml.etree.ElementTree \
    --hidden-import=queue \
    --hidden-import=threading \
    --hidden-import=socket \
    --hidden-import=subprocess \
    --hidden-import=platform \
    --hidden-import=getpass \
    --hidden-import=pathlib \
    --hidden-import=csv \
    --hidden-import=datetime \
    --hidden-import=time \
    --hidden-import=os \
    --hidden-import=sys \
    $([ ! -z "$ICON_FILE" ] && echo "--icon=$ICON_FILE") \
    vs_mac_tools_v2.1.0.py

# Check if the build was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    
    # Check if the .app was created
    if [ -d "dist/VS Mac Tools.app" ]; then
        echo "📱 Application created: dist/VS Mac Tools.app"
        
        # Get the size of the .app bundle
        app_size=$(du -sh "dist/VS Mac Tools.app" | cut -f1)
        echo "📏 Application size: $app_size"
        
        # Verify CustomTkinter assets are included
        if [ -d "dist/VS Mac Tools.app/Contents/Resources/customtkinter/assets" ]; then
            echo "✅ CustomTkinter assets properly included"
            theme_count=$(ls "dist/VS Mac Tools.app/Contents/Resources/customtkinter/assets/themes"/*.json 2>/dev/null | wc -l)
            echo "   📋 Themes included: $theme_count"
        else
            echo "⚠️  Warning: CustomTkinter assets may be missing"
        fi
        
        # Test if the app can be launched
        echo "🧪 Testing application launch..."
        if open "dist/VS Mac Tools.app" 2>/dev/null; then
            echo "✅ Application launched - check if UI displays properly"
        else
            echo "⚠️  Warning: Application may have launch issues"
        fi
        
        echo ""
        echo "🎉 VS Mac Tools v2.1.0 has been built with CustomTkinter fixes!"
        echo ""
        echo "📍 Location: $(pwd)/dist/VS Mac Tools.app"
        echo ""
        echo "🔧 CustomTkinter Fix Applied:"
        echo "   • Added CustomTkinter assets (themes, fonts)"
        echo "   • Included all CustomTkinter submodules"
        echo "   • Added darkdetect for theme detection"
        echo "   • Used proper PyInstaller hooks"
        echo ""
        echo "📋 Next steps:"
        echo "   1. Test the application - UI should now display properly"
        echo "   2. If UI is still blank, check Console.app for errors"
        echo "   3. Copy to Applications: cp -R 'dist/VS Mac Tools.app' /Applications/"
        echo ""
        
    else
        echo "❌ Error: Application bundle not found in dist/ directory"
        exit 1
    fi
else
    echo "❌ Build failed. Check the error messages above."
    exit 1
fi

# Clean up hook directory
rm -rf hooks

echo "🏁 Fixed PyInstaller build process completed!"
