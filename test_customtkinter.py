#!/usr/bin/env python3
"""
Test script to debug CustomTkinter issues in PyInstaller
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def test_basic_tkinter():
    """Test if basic tkinter works"""
    try:
        root = tk.Tk()
        root.title("Basic Tkinter Test")
        root.geometry("300x200")
        
        label = tk.Label(root, text="Basic Tkinter Works!", font=("Arial", 16))
        label.pack(pady=50)
        
        button = tk.Button(root, text="Close", command=root.quit)
        button.pack(pady=20)
        
        print("✅ Basic Tkinter test successful")
        root.mainloop()
        return True
    except Exception as e:
        print(f"❌ Basic Tkinter failed: {e}")
        return False

def test_customtkinter_import():
    """Test if CustomTkinter can be imported"""
    try:
        import customtkinter as ctk
        print(f"✅ CustomTkinter imported successfully")
        print(f"   Version: {ctk.__version__}")
        print(f"   Path: {ctk.__file__}")
        return True
    except Exception as e:
        print(f"❌ CustomTkinter import failed: {e}")
        return False

def test_customtkinter_paths():
    """Test CustomTkinter asset paths"""
    try:
        import customtkinter as ctk
        
        # Get CustomTkinter directory
        ctk_dir = os.path.dirname(ctk.__file__)
        print(f"📁 CustomTkinter directory: {ctk_dir}")
        
        # Check for assets
        assets_dir = os.path.join(ctk_dir, "assets")
        print(f"📁 Assets directory: {assets_dir}")
        print(f"   Exists: {os.path.exists(assets_dir)}")
        
        if os.path.exists(assets_dir):
            themes_dir = os.path.join(assets_dir, "themes")
            print(f"📁 Themes directory: {themes_dir}")
            print(f"   Exists: {os.path.exists(themes_dir)}")
            
            if os.path.exists(themes_dir):
                themes = [f for f in os.listdir(themes_dir) if f.endswith('.json')]
                print(f"   Themes found: {themes}")
        
        return True
    except Exception as e:
        print(f"❌ CustomTkinter path test failed: {e}")
        return False

def test_customtkinter_simple():
    """Test simple CustomTkinter window"""
    try:
        import customtkinter as ctk
        
        # Set appearance mode and theme
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # Create window
        root = ctk.CTk()
        root.title("CustomTkinter Test")
        root.geometry("400x300")
        
        # Add widgets
        label = ctk.CTkLabel(root, text="CustomTkinter Test", font=ctk.CTkFont(size=20, weight="bold"))
        label.pack(pady=20)
        
        button = ctk.CTkButton(root, text="Test Button", command=lambda: print("Button clicked!"))
        button.pack(pady=10)
        
        entry = ctk.CTkEntry(root, placeholder_text="Test Entry")
        entry.pack(pady=10)
        
        close_button = ctk.CTkButton(root, text="Close", command=root.quit)
        close_button.pack(pady=20)
        
        print("✅ CustomTkinter simple test successful")
        root.mainloop()
        return True
    except Exception as e:
        print(f"❌ CustomTkinter simple test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧪 Testing CustomTkinter in PyInstaller environment")
    print("=" * 50)
    
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Executable: {sys.executable}")
    print(f"Frozen: {getattr(sys, 'frozen', False)}")
    
    if getattr(sys, 'frozen', False):
        print(f"Bundle dir: {sys._MEIPASS}")
    
    print("\n1. Testing basic Tkinter...")
    if not test_basic_tkinter():
        return
    
    print("\n2. Testing CustomTkinter import...")
    if not test_customtkinter_import():
        return
    
    print("\n3. Testing CustomTkinter paths...")
    test_customtkinter_paths()
    
    print("\n4. Testing CustomTkinter simple window...")
    test_customtkinter_simple()

if __name__ == "__main__":
    main()
